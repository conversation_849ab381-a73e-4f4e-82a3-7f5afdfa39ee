#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取缺失的主板股票数据
"""

import requests
import pandas as pd
import json
import time
import os
from datetime import datetime
import logging

class MainboardStockCrawler:
    """主板股票爬虫 - 专门获取缺失的主板股票"""

    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'http://quote.eastmoney.com/',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)

        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('mainboard_crawler.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def get_mainboard_stocks(self):
        """获取主板股票数据"""
        self.logger.info("开始获取主板股票数据...")

        mainboard_stocks = []

        # 定义主板市场参数
        mainboard_configs = [
            {
                'name': '深圳主板',
                'fs': 'm:0 t:6',  # 深圳主板
                'exchange': '深圳证券交易所',
                'market_type': '深圳主板'
            },
            {
                'name': '深圳中小板',
                'fs': 'm:0 t:13',  # 深圳中小板
                'exchange': '深圳证券交易所',
                'market_type': '深圳中小板'
            },
            {
                'name': '上海主板A',
                'fs': 'm:1 t:2',  # 上海主板A
                'exchange': '上海证券交易所',
                'market_type': '上海主板'
            },
            {
                'name': '上海主板B',
                'fs': 'm:1 t:3',  # 上海主板B
                'exchange': '上海证券交易所',
                'market_type': '上海主板'
            }
        ]

        for config in mainboard_configs:
            self.logger.info(f"正在获取 {config['name']} 股票...")
            try:
                stocks = self._get_market_stocks(config)
                if stocks:
                    mainboard_stocks.extend(stocks)
                    self.logger.info(f"{config['name']}: 获取到 {len(stocks)} 只股票")
                else:
                    self.logger.warning(f"{config['name']}: 未获取到股票数据")
            except Exception as e:
                self.logger.error(f"获取 {config['name']} 股票失败: {e}")

            time.sleep(2)  # 增加延迟

        # 去重
        mainboard_stocks = self._deduplicate_stocks(mainboard_stocks)

        self.logger.info(f"主板股票获取完成，共 {len(mainboard_stocks)} 只股票")
        return mainboard_stocks

    def _get_market_stocks(self, config, max_pages=20):
        """获取特定市场的股票"""
        market_stocks = []

        for page in range(1, max_pages + 1):
            try:
                url = "http://80.push2.eastmoney.com/api/qt/clist/get"
                params = {
                    'pn': page,
                    'pz': 200,  # 每页更多股票
                    'po': 1,
                    'np': 1,
                    'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                    'fltt': 2,
                    'invt': 2,
                    'fid': 'f3',
                    'fs': config['fs'],
                    'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152'
                }

                self.logger.info(f"请求 {config['name']} 第 {page} 页...")
                response = self.session.get(url, params=params, timeout=15)

                if response.status_code != 200:
                    self.logger.warning(f"第 {page} 页请求失败: {response.status_code}")
                    break

                data = response.json()

                if data['rc'] != 0:
                    self.logger.info(f"{config['name']} 第 {page} 页无更多数据")
                    break

                if 'data' not in data or 'diff' not in data['data']:
                    break

                stocks = data['data']['diff']
                if not stocks:
                    break

                self.logger.info(f"第 {page} 页获取到 {len(stocks)} 只股票")

                # 解析股票数据
                for stock in stocks:
                    try:
                        stock_info = self._parse_stock_data(stock, config)
                        if stock_info:
                            market_stocks.append(stock_info)
                    except Exception as e:
                        self.logger.warning(f"解析股票数据失败: {stock.get('f12', 'unknown')} - {e}")

                # 如果这一页的数据少于200，说明已经是最后一页了
                if len(stocks) < 200:
                    break

                time.sleep(1)  # 页面间延迟

            except Exception as e:
                self.logger.error(f"获取第 {page} 页数据失败: {e}")
                break

        return market_stocks

    def _parse_stock_data(self, stock, config):
        """解析股票数据"""
        try:
            stock_code = stock['f12']

            # 验证股票代码格式
            if not self._is_valid_mainboard_code(stock_code):
                return None

            stock_info = {
                '股票代码': stock_code,
                '股票名称': stock['f14'],
                '最新价': stock.get('f2', 0),
                '涨跌幅': stock.get('f3', 0),
                '涨跌额': stock.get('f4', 0),
                '成交量': stock.get('f5', 0),
                '成交额': stock.get('f6', 0),
                '振幅': stock.get('f7', 0),
                '最高价': stock.get('f15', 0),
                '最低价': stock.get('f16', 0),
                '今开价': stock.get('f17', 0),
                '昨收价': stock.get('f18', 0),
                '量比': stock.get('f10', 0),
                '换手率': stock.get('f8', 0),
                '市盈率': stock.get('f9', 0),
                '市净率': stock.get('f23', 0),
                '总市值': stock.get('f20', 0),
                '流通市值': stock.get('f21', 0),
                '总股本': stock.get('f22', 0),
                '流通股本': stock.get('f25', 0),
                '市场类型': config['market_type'],
                '交易所': config['exchange'],
                '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            return stock_info

        except Exception as e:
            self.logger.error(f"解析股票 {stock.get('f12', 'unknown')} 数据失败: {e}")
            return None

    def _is_valid_mainboard_code(self, stock_code):
        """验证是否为有效的A股代码"""
        if not stock_code or not stock_code.isdigit():
            return False

        # 主板股票代码规则
        if stock_code.startswith(('000', '002', '600', '601', '603')):
            return len(stock_code) == 6

        return False

    def _deduplicate_stocks(self, stocks):
        """去重股票数据"""
        if not stocks:
            return []

        df = pd.DataFrame(stocks)
        df_deduplicated = df.drop_duplicates(subset=['股票代码'], keep='last')

        self.logger.info(f"去重前: {len(stocks)} 只，去重后: {len(df_deduplicated)} 只")
        return df_deduplicated.to_dict('records')

    def merge_with_existing_data(self, new_stocks):
        """与现有数据合并"""
        try:
            # 读取现有数据
            existing_df = pd.read_csv('data/all_a_stocks.csv', dtype={'股票代码': str})
            existing_codes = set(existing_df['股票代码'].tolist())

            # 过滤出新增的股票
            new_df = pd.DataFrame(new_stocks)
            new_codes = set(new_df['股票代码'].tolist())

            # 找出新增的股票
            actually_new = new_codes - existing_codes
            new_stocks_filtered = [stock for stock in new_stocks if stock['股票代码'] in actually_new]

            if new_stocks_filtered:
                # 合并数据
                combined_df = pd.concat([existing_df, new_df], ignore_index=True)
                combined_df = combined_df.drop_duplicates(subset=['股票代码'], keep='last')

                # 保存合并后的数据
                combined_df.to_csv('data/all_a_stocks_updated.csv', index=False, encoding='utf-8-sig')

                self.logger.info(f"成功添加 {len(new_stocks_filtered)} 只新股票")
                self.logger.info(f"总股票数量更新为: {len(combined_df)} 只")

                return combined_df, len(new_stocks_filtered)
            else:
                self.logger.info("没有新增的股票")
                return existing_df, 0

        except FileNotFoundError:
            self.logger.warning("未找到现有数据文件，将创建新文件")
            new_df = pd.DataFrame(new_stocks)
            new_df.to_csv('data/all_a_stocks_updated.csv', index=False, encoding='utf-8-sig')
            return new_df, len(new_stocks)

    def save_mainboard_data(self, stocks, filename='mainboard_stocks.csv'):
        """保存主板股票数据"""
        if not stocks:
            self.logger.warning("没有主板股票数据可保存")
            return False

        try:
            os.makedirs('data', exist_ok=True)
            df = pd.DataFrame(stocks)

            filepath = os.path.join('data', filename)
            df.to_csv(filepath, index=False, encoding='utf-8-sig')

            self.logger.info(f"主板股票数据已保存到: {filepath}")

            # 统计信息
            self._print_statistics(df)

            return True

        except Exception as e:
            self.logger.error(f"保存主板股票数据失败: {e}")
            return False

    def _print_statistics(self, df):
        """打印统计信息"""
        print("\n" + "="*50)
        print("📊 主板股票数据统计")
        print("="*50)

        total_stocks = len(df)

        # 按交易所统计
        exchange_stats = df['交易所'].value_counts()
        print("交易所分布:")
        for exchange, count in exchange_stats.items():
            percentage = count / total_stocks * 100
            print(".1f")
        # 按市场类型统计
        market_stats = df['市场类型'].value_counts()
        print("\n市场类型分布:")
        for market, count in market_stats.items():
            percentage = count / total_stocks * 100
            print(".1f")
        # 按代码段统计
        code_prefixes = ['000', '002', '600', '601', '603']
        print("\n代码段分布:")
        for prefix in code_prefixes:
            count = len(df[df['股票代码'].str.startswith(prefix)])
            if count > 0:
                percentage = count / total_stocks * 100
                print(".1f")
        print("="*50)

def main():
    """主函数"""
    print("=== 主板股票数据获取工具 ===\n")

    crawler = MainboardStockCrawler()

    # 获取主板股票
    print("正在获取主板股票数据...")
    mainboard_stocks = crawler.get_mainboard_stocks()

    if mainboard_stocks:
        print(f"✅ 获取到 {len(mainboard_stocks)} 只主板股票")

        # 保存主板股票数据
        success = crawler.save_mainboard_data(mainboard_stocks)
        if success:
            print("✅ 主板股票数据保存成功")

        # 合并到现有数据
        print("\n正在合并到现有数据...")
        merged_df, new_count = crawler.merge_with_existing_data(mainboard_stocks)

        if new_count > 0:
            print(f"✅ 成功添加 {new_count} 只新股票")
            print(f"📊 总股票数量更新为: {len(merged_df)} 只")

            # 检查长沙银行是否在新增数据中
            changsha_bank = merged_df[merged_df['股票代码'] == '601577']
            if len(changsha_bank) > 0:
                print("🎉 长沙银行(601577)已成功添加到数据中！")
                print(changsha_bank[['股票代码', '股票名称', '交易所', '市场类型']].to_string(index=False))
        else:
            print("ℹ️ 没有新增的股票")

    else:
        print("❌ 未获取到主板股票数据")

    print(f"\n程序运行完成！详细日志请查看 mainboard_crawler.log")

if __name__ == "__main__":
    main()
