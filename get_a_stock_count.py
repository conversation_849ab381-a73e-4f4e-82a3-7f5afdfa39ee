import requests
import json
from datetime import datetime

class AStockCounter:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'http://quote.eastmoney.com/',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
        }
    
    def get_a_stock_count(self):
        """获取A股股票总数量"""
        url = "http://80.push2.eastmoney.com/api/qt/clist/get"
        
        # 获取所有A股股票数量（设置一个足够大的页面大小）
        params = {
            'pn': 1,
            'pz': 10000,  # 设置足够大的数量
            'po': 1,
            'np': 1,
            'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
            'fltt': 2,
            'invt': 2,
            'fid': 'f3',
            'fs': 'm:0 t:6,m:0 t:80,m:1 t:2,m:1 t:23',  # A股股票筛选条件
            'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14'
        }
        
        try:
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            data = response.json()
            
            if data.get('rc') == 0 and 'data' in data and data['data']:
                total_count = data['data']['total']  # 总数量
                current_stocks = len(data['data']['diff']) if data['data']['diff'] else 0
                
                result = {
                    'total_stocks': total_count,
                    'fetched_stocks': current_stocks,
                    'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'data_source': '东方财富'
                }
                
                return result
            else:
                return {'error': '获取数据失败', 'response': data}
                
        except Exception as e:
            return {'error': f'请求失败: {str(e)}'}
    
    def get_stock_count_by_market(self):
        """分市场获取股票数量"""
        markets = {
            'shanghai': 'm:1 t:2,m:1 t:23',  # 上海A股
            'shenzhen': 'm:0 t:6,m:0 t:80'   # 深圳A股
        }
        
        results = {}
        total_count = 0
        
        for market_name, market_code in markets.items():
            url = "http://80.push2.eastmoney.com/api/qt/clist/get"
            params = {
                'pn': 1,
                'pz': 5000,
                'po': 1,
                'np': 1,
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': 2,
                'invt': 2,
                'fid': 'f3',
                'fs': market_code,
                'fields': 'f12,f14'
            }
            
            try:
                response = requests.get(url, params=params, headers=self.headers, timeout=10)
                data = response.json()
                
                if data.get('rc') == 0 and 'data' in data and data['data']:
                    market_total = data['data']['total']
                    results[market_name] = {
                        'count': market_total,
                        'market_name': '上海A股' if market_name == 'shanghai' else '深圳A股'
                    }
                    total_count += market_total
                else:
                    results[market_name] = {'error': '获取失败'}
                    
            except Exception as e:
                results[market_name] = {'error': str(e)}
        
        results['total'] = total_count
        results['update_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        return results
    
    def get_detailed_stock_info(self):
        """获取详细的股票信息统计"""
        url = "http://80.push2.eastmoney.com/api/qt/clist/get"
        params = {
            'pn': 1,
            'pz': 6000,
            'po': 1,
            'np': 1,
            'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
            'fltt': 2,
            'invt': 2,
            'fid': 'f3',
            'fs': 'm:0 t:6,m:0 t:80,m:1 t:2,m:1 t:23',
            'fields': 'f12,f13,f14'
        }
        
        try:
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            data = response.json()
            
            if data.get('rc') == 0 and 'data' in data and data['data']:
                stocks = data['data']['diff']
                
                # 按市场分类统计
                shanghai_count = 0
                shenzhen_count = 0
                
                for stock in stocks:
                    stock_code = stock.get('f12', '')
                    if stock_code.startswith('6'):
                        shanghai_count += 1
                    elif stock_code.startswith(('0', '3')):
                        shenzhen_count += 1
                
                result = {
                    'total_stocks': len(stocks),
                    'shanghai_stocks': shanghai_count,
                    'shenzhen_stocks': shenzhen_count,
                    'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'breakdown': {
                        '上海A股(6开头)': shanghai_count,
                        '深圳A股(0/3开头)': shenzhen_count
                    }
                }
                
                return result
            else:
                return {'error': '获取数据失败'}
                
        except Exception as e:
            return {'error': f'请求失败: {str(e)}'}

def main():
    """主函数"""
    print("=== A股股票数量统计 ===\n")
    
    counter = AStockCounter()
    
    print("1. 获取A股总体数量...")
    total_result = counter.get_a_stock_count()
    
    if 'error' not in total_result:
        print(f"A股总股票数量: {total_result['total_stocks']} 只")
        print(f"数据更新时间: {total_result['update_time']}")
        print(f"数据来源: {total_result['data_source']}")
    else:
        print(f"获取失败: {total_result['error']}")
    
    print("\n" + "="*50)
    print("2. 分市场统计...")
    market_result = counter.get_stock_count_by_market()
    
    if 'total' in market_result:
        print(f"总计: {market_result['total']} 只股票")
        for market, info in market_result.items():
            if market not in ['total', 'update_time'] and isinstance(info, dict):
                if 'count' in info:
                    print(f"  {info['market_name']}: {info['count']} 只")
                else:
                    print(f"  {market}: {info.get('error', '未知错误')}")
        print(f"统计时间: {market_result['update_time']}")
    
    print("\n" + "="*50)
    print("3. 详细分类统计...")
    detailed_result = counter.get_detailed_stock_info()
    
    if 'error' not in detailed_result:
        print(f"实际获取股票数量: {detailed_result['total_stocks']} 只")
        print("详细分布:")
        for category, count in detailed_result['breakdown'].items():
            print(f"  {category}: {count} 只")
        print(f"统计时间: {detailed_result['update_time']}")
    else:
        print(f"详细统计失败: {detailed_result['error']}")
    
    print("\n" + "="*50)
    print("官方数据对比:")
    print("根据中国上市公司协会最新数据（2024年12月31日）:")
    print("  境内股票市场共有上市公司: 5392家")
    print("  上海证券交易所: 2278家")
    print("  深圳证券交易所: 2852家") 
    print("  北京证券交易所: 262家")

if __name__ == "__main__":
    main()
