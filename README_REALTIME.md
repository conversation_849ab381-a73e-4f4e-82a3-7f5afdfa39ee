# AI量化交易系统 - 实时数据功能

## 🎯 功能概述

实时数据功能为AI量化交易系统添加了实时的市场监控和交易信号生成功能，包括：

- 📊 **实时行情获取**: 获取A股市场的实时股价、成交量等数据
- 🤖 **实时交易信号**: 基于AI模型生成实时的买卖信号
- 📈 **实时数据分析**: 分析市场情绪和价格变化趋势
- 🔄 **连续监控**: 支持长时间运行的实时监控模式

## 🚀 快速开始

### 1. 基础实时行情获取

```python
from ai_quant_trading_system import DataManager

# 初始化数据管理器
data_manager = DataManager()

# 获取实时行情
stock_list = ['000001', '000002', '600036', '600519', '000858']
realtime_data = data_manager.get_realtime_data(stock_list)

# 显示结果
for stock in realtime_data:
    print(f"{stock['stock_code']} {stock['stock_name']}: ¥{stock['current_price']} ({stock['change_percent']}%)")
```

### 2. 实时交易信号监控

```python
from ai_quant_trading_system import RealTimeTradingSystem

# 初始化实时交易系统（需要先训练好模型）
realtime_system = RealTimeTradingSystem('quant_system')

# 开始实时监控
stock_list = ['000001', '000002', '600036', '600519', '000858']
realtime_system.start_realtime_monitoring(stock_list, interval=30)  # 30秒间隔
```

### 3. 运行演示程序

```bash
# 运行实时功能演示
python demo_realtime_trading.py
```

## 📋 功能特性

### 实时行情获取
- ✅ 支持多只股票同时获取
- ✅ 包含完整的市场数据（价格、涨跌幅、成交量等）
- ✅ 自动处理上海/深圳市场代码转换
- ✅ 网络异常自动重试机制

### 实时交易信号
- ✅ 基于训练好的AI模型生成信号
- ✅ 可配置信号阈值（默认2%）
- ✅ 实时特征计算和预测
- ✅ 信号强度分析和排序

### 监控功能
- ✅ 可配置监控间隔
- ✅ 支持多线程运行
- ✅ 优雅的停止机制
- ✅ 实时日志记录

## 🔧 配置选项

### 信号阈值设置

```python
# 设置更严格的信号阈值
realtime_system.set_signal_threshold(0.05)  # 5%
```

### 监控参数配置

```python
# 自定义监控参数
realtime_system.start_realtime_monitoring(
    stock_list=['000001', '000002'],  # 监控股票列表
    interval=60                        # 监控间隔（秒）
)
```

## 📊 数据格式

### 实时行情数据结构

```python
{
    'stock_code': '000001',        # 股票代码
    'stock_name': '平安银行',       # 股票名称
    'current_price': 10.50,       # 当前价格
    'change_percent': 2.34,       # 涨跌幅(%)
    'change_amount': 0.24,        # 涨跌额
    'volume': 1234567,            # 成交量
    'amount': 12345678.90,        # 成交额
    'open_price': 10.30,          # 开盘价
    'prev_close': 10.26,          # 昨收价
    'high_price': 10.60,          # 最高价
    'low_price': 10.20,           # 最低价
    'update_time': '2025-01-12 14:30:00'  # 更新时间
}
```

### 交易信号数据结构

```python
{
    'stock_code': '000001',        # 股票代码
    'stock_name': '平安银行',       # 股票名称
    'signal': 1,                  # 信号类型 (1=买入, -1=卖出, 0=持有)
    'current_price': 10.50,       # 当前价格
    'change_percent': 2.34,       # 涨跌幅(%)
    'prediction': 0.034,          # 预测收益率
    'timestamp': '14:30:15'       # 信号生成时间
}
```

## ⚠️ 注意事项

### 数据获取限制
- 实时数据依赖网络连接
- 东方财富API可能有频率限制
- 非交易时间可能无法获取数据

### 模型依赖
- 实时信号生成需要预训练的AI模型
- 模型文件需要保存在指定路径
- 建议定期重新训练模型

### 风险提示
- 实时交易信号仅供参考
- 建议结合人工判断
- 注意交易风险和资金管理

## 🔧 故障排除

### 常见问题

1. **无法获取实时数据**
   - 检查网络连接
   - 确认股票代码格式正确
   - 查看系统日志信息

2. **模型加载失败**
   - 确认模型文件存在
   - 检查模型文件路径
   - 重新运行训练流程

3. **信号生成异常**
   - 检查特征数据完整性
   - 验证模型训练状态
   - 调整信号阈值参数

### 日志查看

系统会自动记录详细的运行日志：

```python
import logging
logging.basicConfig(level=logging.INFO)
```

## 🎯 进阶功能

### 自定义信号策略

```python
class CustomRealTimeSystem(RealTimeTradingSystem):
    def _generate_signal(self, prediction, current_price, change_percent):
        # 自定义信号生成逻辑
        if prediction > 0.03 and change_percent > 1:
            return 1  # 强势买入
        elif prediction < -0.03 or change_percent < -3:
            return -1  # 止损卖出
        else:
            return 0
```

### 多策略组合

```python
# 同时运行多个策略
strategies = [
    RealTimeTradingSystem('model_1'),
    RealTimeTradingSystem('model_2'),
    CustomRealTimeSystem('model_3')
]

for strategy in strategies:
    # 并行运行不同策略
    pass
```

## 📈 性能优化

### 数据缓存
- 系统自动缓存历史数据
- 减少重复API调用
- 提高响应速度

### 多线程处理
- 支持并发数据获取
- 提高处理效率
- 降低监控延迟

### 内存管理
- 自动清理过期数据
- 控制内存使用量
- 支持长时间运行

## 🤝 贡献与反馈

欢迎提交问题和建议，帮助改进实时数据功能！

---

*最后更新: 2025-01-12*
