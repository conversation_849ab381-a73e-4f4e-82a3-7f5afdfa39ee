#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将所有A股股票合并到一个CSV文件中
"""

import pandas as pd
import os
from datetime import datetime
import logging

class StockMerger:
    """A股股票数据合并器"""

    def __init__(self):
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('stock_merge.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def merge_all_stocks(self):
        """合并所有股票数据"""
        self.logger.info("开始合并A股股票数据...")

        # 定义各市场文件映射
        market_files = {
            '上海主板': 'data/stock_complete_上海主板.csv',
            '深圳主板': 'data/stock_complete_深圳主板.csv',
            '深圳中小板': 'data/stock_complete_深圳中小板.csv',
            '深圳创业板': 'data/stock_complete_深圳创业板.csv',
            '北京证券交易所': 'data/stock_complete_北京证券交易所.csv',
            '其他': 'data/stock_complete_其他.csv'
        }

        all_stocks = []

        # 读取各个市场的股票数据
        for market_name, file_path in market_files.items():
            if os.path.exists(file_path):
                try:
                    self.logger.info(f"正在读取 {market_name} 数据...")
                    df = pd.read_csv(file_path, encoding='utf-8-sig')

                    # 添加交易所字段
                    df['交易所'] = self._get_exchange_name(market_name)

                    # 确保所有列都存在
                    df = self._ensure_columns(df)

                    all_stocks.append(df)
                    self.logger.info(f"{market_name}: 读取到 {len(df)} 只股票")

                except Exception as e:
                    self.logger.error(f"读取 {market_name} 数据失败: {e}")
                    continue
            else:
                self.logger.warning(f"文件不存在: {file_path}")

        if not all_stocks:
            self.logger.error("没有找到任何股票数据文件")
            return None

        # 合并所有数据
        self.logger.info("正在合并数据...")
        merged_df = pd.concat(all_stocks, ignore_index=True)

        # 数据清理和去重
        merged_df = self._clean_and_deduplicate(merged_df)

        self.logger.info(f"合并完成，共 {len(merged_df)} 只股票")
        return merged_df

    def _get_exchange_name(self, market_name):
        """获取交易所名称"""
        exchange_map = {
            '上海主板': '上海证券交易所',
            '深圳主板': '深圳证券交易所',
            '深圳中小板': '深圳证券交易所',
            '深圳创业板': '深圳证券交易所',
            '北京证券交易所': '北京证券交易所',
            '其他': '其他'
        }
        return exchange_map.get(market_name, '其他')

    def _ensure_columns(self, df):
        """确保所有必要的列都存在"""
        required_columns = [
            '股票代码', '股票名称', '最新价', '涨跌幅', '涨跌额',
            '成交量', '成交额', '振幅', '最高价', '最低价',
            '今开价', '昨收价', '量比', '换手率', '市盈率',
            '市净率', '总市值', '流通市值', '总股本', '流通股本',
            '市场类型', '更新时间'
        ]

        # 添加缺失的列
        for col in required_columns:
            if col not in df.columns:
                if col in ['最新价', '涨跌幅', '涨跌额', '成交量', '成交额', '市盈率', '市净率', '总市值', '流通市值']:
                    df[col] = 0.0
                else:
                    df[col] = ''

        # 重新排列列的顺序
        df = df[required_columns + ['交易所']]

        return df

    def _clean_and_deduplicate(self, df):
        """数据清理和去重"""
        self.logger.info("正在清理和去重数据...")

        # 移除完全重复的行
        initial_count = len(df)
        df = df.drop_duplicates()
        self.logger.info(f"移除重复行: {initial_count - len(df)}")

        # 按股票代码去重，保留最新的记录
        df = df.drop_duplicates(subset=['股票代码'], keep='last')
        self.logger.info(f"按股票代码去重后剩余: {len(df)} 只股票")

        # 数据类型转换
        numeric_columns = [
            '最新价', '涨跌幅', '涨跌额', '成交量', '成交额',
            '振幅', '最高价', '最低价', '今开价', '昨收价',
            '量比', '换手率', '市盈率', '市净率',
            '总市值', '流通市值', '总股本', '流通股本'
        ]

        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)

        # 清理字符串字段
        string_columns = ['股票名称', '市场类型', '交易所']
        for col in string_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip()

        # 添加合并时间
        df['合并时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        return df

    def save_merged_data(self, df, filename='all_a_stocks.csv'):
        """保存合并后的数据"""
        try:
            # 创建输出目录
            os.makedirs('data', exist_ok=True)

            # 保存完整版
            filepath = os.path.join('data', filename)
            df.to_csv(filepath, index=False, encoding='utf-8-sig')
            self.logger.info(f"完整版已保存到: {filepath}")

            # 保存简化版（主要字段）
            simple_columns = [
                '股票代码', '股票名称', '交易所', '市场类型', '最新价',
                '涨跌幅', '成交量', '市盈率', '总市值', '更新时间'
            ]
            simple_df = df[simple_columns]
            simple_filepath = os.path.join('data', 'all_a_stocks_simple.csv')
            simple_df.to_csv(simple_filepath, index=False, encoding='utf-8-sig')
            self.logger.info(f"简化版已保存到: {simple_filepath}")

            # 按交易所保存
            self._save_by_exchange(df)

            return True

        except Exception as e:
            self.logger.error(f"保存数据失败: {e}")
            return False

    def _save_by_exchange(self, df):
        """按交易所保存数据"""
        exchanges = df['交易所'].unique()

        for exchange in exchanges:
            exchange_df = df[df['交易所'] == exchange]
            if len(exchange_df) > 0:
                # 生成文件名
                filename = f"all_stocks_{exchange.replace('证券交易所', '').replace('其他', 'other')}.csv"
                filepath = os.path.join('data', filename)
                exchange_df.to_csv(filepath, index=False, encoding='utf-8-sig')
                self.logger.info(f"{exchange}股票已保存到: {filepath}")

    def print_statistics(self, df):
        """打印统计信息"""
        print("\n" + "="*60)
        print("📊 A股股票数据统计报告")
        print("="*60)
        print(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"数据来源: 东方财富网")
        print()

        # 基本统计
        total_stocks = len(df)
        print("📊 基本统计:")
        print(f"  总股票数量: {total_stocks}")

        # 交易所统计
        exchange_stats = df['交易所'].value_counts()
        print("\n🏛️ 交易所统计:")
        for exchange, count in exchange_stats.items():
            percentage = count / total_stocks * 100
            print(".1f")
        # 市场类型统计
        market_stats = df['市场类型'].value_counts()
        print("\n🏢 市场类型统计:")
        for market, count in market_stats.items():
            percentage = count / total_stocks * 100
            print(".1f")
        # 价格统计
        price_stats = df[df['最新价'] > 0]['最新价']
        if len(price_stats) > 0:
            print("\n💰 股价统计:")
            print(".2f")
            print(".2f")
            print(".2f")
        # 市值统计
        mv_stats = df[df['总市值'] > 0]['总市值']
        if len(mv_stats) > 0:
            print("\n🏢 市值统计:")
            print(".1f")
            print(".1f")
            print(".1f")
        # 涨跌统计
        up_count = len(df[df['涨跌幅'] > 0])
        down_count = len(df[df['涨跌幅'] < 0])
        flat_count = len(df[df['涨跌幅'] == 0])
        print("\n📈 涨跌统计:")
        print(".1f")
        print(".1f")
        print(".1f")
        print("\n" + "="*60)

def main():
    """主函数"""
    print("=== A股股票数据合并程序 ===\n")

    merger = StockMerger()

    # 合并所有股票数据
    print("正在合并所有A股股票数据...")
    merged_df = merger.merge_all_stocks()

    if merged_df is None or len(merged_df) == 0:
        print("❌ 没有找到股票数据")
        return

    print(f"✅ 成功合并 {len(merged_df)} 只股票数据")

    # 保存合并后的数据
    print("\n正在保存合并后的数据...")
    success = merger.save_merged_data(merged_df)

    if success:
        print("✅ 数据保存成功！")
        print("📁 文件位置:")
        print("   - data/all_a_stocks.csv (完整版)")
        print("   - data/all_a_stocks_simple.csv (简化版)")

        # 显示各交易所文件
        data_dir = 'data'
        if os.path.exists(data_dir):
            csv_files = [f for f in os.listdir(data_dir) if f.startswith('all_stocks_') and f.endswith('.csv')]
            for csv_file in csv_files:
                print(f"   - data/{csv_file}")

        # 打印统计信息
        merger.print_statistics(merged_df)

    else:
        print("❌ 数据保存失败")

    print(f"\n程序运行完成！详细日志请查看 stock_merge.log")

if __name__ == "__main__":
    main()
