import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
import json
import pickle
from typing import Dict, List, Tuple, Optional
import logging
import requests
import time
import os

# 机器学习相关
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error
import joblib

# 数据可视化
import matplotlib.pyplot as plt
import seaborn as sns
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

class DataManager:
    """数据管理模块 - 整合真实A股数据源"""

    def __init__(self):
        self.data_cache = {}
        self.logger = self._setup_logger()
        self.session = requests.Session()
        self._setup_session()

    def _setup_logger(self):
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        return logging.getLogger(__name__)

    def _setup_session(self):
        """设置HTTP会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'http://quote.eastmoney.com/',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
        }
        self.session.headers.update(headers)

    def get_stock_kline(self, stock_code: str, period: str = '101', count: int = 200) -> List[Dict]:
        """获取股票K线数据"""
        # 判断股票所属市场
        if stock_code.startswith('6'):
            stock_code = f"1.{stock_code}"  # 上海
        else:
            stock_code = f"0.{stock_code}"  # 深圳

        url = "http://push2his.eastmoney.com/api/qt/stock/kline/get"
        params = {
            'secid': stock_code,
            'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
            'fields1': 'f1,f2,f3,f4,f5,f6',
            'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
            'klt': period,
            'fqt': 1,
            'end': '20500101',
            'lmt': count
        }

        try:
            response = self.session.get(url, params=params, timeout=10)
            data = response.json()

            if data['rc'] == 0 and 'data' in data and data['data']:
                klines = data['data']['klines']
                kline_data = []

                for kline in klines:
                    items = kline.split(',')
                    kline_info = {
                        'date': items[0],
                        'open': float(items[1]),
                        'close': float(items[2]),
                        'high': float(items[3]),
                        'low': float(items[4]),
                        'volume': int(items[5]),
                        'amount': float(items[6]),
                        'amplitude': float(items[7]),
                        'change_percent': float(items[8]),
                        'change_amount': float(items[9]),
                        'turnover_rate': float(items[10])
                    }
                    kline_data.append(kline_info)

                return kline_data
            else:
                self.logger.warning(f"获取K线数据失败: {stock_code}")
                return []

        except Exception as e:
            self.logger.error(f"获取K线数据出错: {e}")
            return []

    def load_stock_data(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """加载股票数据 - 使用真实A股数据"""
        # 检查缓存
        cache_key = f"{stock_code}_{start_date}_{end_date}"
        if cache_key in self.data_cache:
            return self.data_cache[cache_key]

        try:
            # 获取K线数据
            kline_data = self.get_stock_kline(stock_code, count=500)

            if not kline_data:
                self.logger.warning(f"无法获取股票 {stock_code} 的数据，使用模拟数据")
                return self._generate_mock_data(start_date, end_date)

            # 转换为DataFrame
            df = pd.DataFrame(kline_data)

            # 转换日期格式
            df['date'] = pd.to_datetime(df['date'])

            # 过滤日期范围
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            df = df[(df['date'] >= start_dt) & (df['date'] <= end_dt)]

            if len(df) == 0:
                self.logger.warning(f"指定日期范围内无数据: {stock_code}")
                return self._generate_mock_data(start_date, end_date)

            # 重命名列以匹配原有格式
            df = df.rename(columns={
                'open': 'open',
                'high': 'high',
                'low': 'low',
                'close': 'close',
                'volume': 'volume',
                'amount': 'amount'
            })

            # 计算收益率
            df['return'] = df['close'].pct_change()

            # 缓存数据
            self.data_cache[cache_key] = df
            self.logger.info(f"成功加载股票 {stock_code} 数据: {len(df)} 条记录")

            return df

        except Exception as e:
            self.logger.error(f"加载股票 {stock_code} 数据失败: {e}")
            return self._generate_mock_data(start_date, end_date)

    def _generate_mock_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """生成模拟数据（备用方案）"""
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        np.random.seed(42)

        data = {
            'date': dates,
            'open': np.random.uniform(10, 50, len(dates)),
            'high': np.random.uniform(10, 60, len(dates)),
            'low': np.random.uniform(5, 45, len(dates)),
            'close': np.random.uniform(10, 55, len(dates)),
            'volume': np.random.uniform(1000000, 10000000, len(dates)),
            'amount': np.random.uniform(10000000, 100000000, len(dates))
        }

        df = pd.DataFrame(data)
        df['return'] = df['close'].pct_change()
        return df

    def get_fund_flow(self, stock_code: str, days: int = 30) -> pd.DataFrame:
        """获取资金流向数据"""
        if stock_code.startswith('6'):
            secid = f"1.{stock_code}"
        else:
            secid = f"0.{stock_code}"

        url = "http://push2his.eastmoney.com/api/qt/stock/fflow/kline/get"
        params = {
            'secid': secid,
            'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
            'fields1': 'f1,f2,f3,f7',
            'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63',
            'klt': 101,
            'lmt': days
        }

        try:
            response = self.session.get(url, params=params, timeout=10)
            data = response.json()

            fund_flow_data = []
            if data['rc'] == 0 and 'data' in data and data['data']:
                klines = data['data']['klines']

                for kline in klines:
                    items = kline.split(',')
                    flow_info = {
                        'date': items[0],
                        'main_net_inflow': float(items[1]) if items[1] != '-' else 0,
                        'small_net_inflow': float(items[2]) if items[2] != '-' else 0,
                        'medium_net_inflow': float(items[3]) if items[3] != '-' else 0,
                        'large_net_inflow': float(items[4]) if items[4] != '-' else 0,
                        'super_large_net_inflow': float(items[5]) if items[5] != '-' else 0
                    }
                    fund_flow_data.append(flow_info)

                return pd.DataFrame(fund_flow_data)

        except Exception as e:
            self.logger.error(f"获取资金流向数据失败: {e}")
            return pd.DataFrame()

    def get_valuation_data(self, stock_code: str) -> Dict:
        """获取估值数据"""
        if stock_code.startswith('6'):
            secid = f"1.{stock_code}"
        else:
            secid = f"0.{stock_code}"

        url = "http://push2.eastmoney.com/api/qt/stock/get"
        params = {
            'secid': secid,
            'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
            'fields': 'f43,f57,f58,f162,f92,f173,f104,f105,f84,f85,f183,f184,f185,f186,f187,f188'
        }

        try:
            response = self.session.get(url, params=params, timeout=10)
            data = response.json()

            if data['rc'] == 0 and 'data' in data:
                stock_data = data['data']
                valuation_info = {
                    'pe_ratio': stock_data.get('f162'),  # 市盈率TTM
                    'pb_ratio': stock_data.get('f173'),  # 市净率
                    'ps_ratio': stock_data.get('f184'),  # 市销率
                    'pc_ratio': stock_data.get('f185'),  # 市现率
                    'total_mv': stock_data.get('f43'),   # 总市值
                    'float_mv': stock_data.get('f85'),   # 流通市值
                    'total_shares': stock_data.get('f84'),  # 总股本
                    'dividend_yield': stock_data.get('f187')  # 股息率
                }
                return valuation_info

        except Exception as e:
            self.logger.error(f"获取估值数据失败: {e}")
            return {}

    def get_financial_data(self, stock_code: str, report_type: str = '1') -> pd.DataFrame:
        """获取财务数据"""
        if stock_code.startswith('6'):
            market_code = f"SH{stock_code}"
        else:
            market_code = f"SZ{stock_code}"

        url = "http://f10.eastmoney.com/pc_hsgt/NewFinanceAnalysis/MainTargetAjax"
        params = {
            'companyType': '4',
            'reportDateType': '0',
            'reportType': report_type,
            'dates': '',
            'code': market_code
        }

        try:
            response = self.session.get(url, params=params, timeout=10)
            data = response.json()

            financial_data = []
            if data and 'data' in data:
                dates = data['data']['dates']
                items = data['data']['data']

                for item in items:
                    item_name = item['name']
                    values = item['data']

                    for i, date in enumerate(dates):
                        if i < len(values) and values[i] is not None:
                            financial_data.append({
                                'report_date': date,
                                'indicator': item_name,
                                'value': values[i],
                                'stock_code': stock_code
                            })

            return pd.DataFrame(financial_data)

        except Exception as e:
            self.logger.error(f"获取财务数据失败: {e}")
            return pd.DataFrame()

    def get_realtime_data(self, stock_codes):
        """获取股票实时数据"""
        if isinstance(stock_codes, str):
            stock_codes = [stock_codes]

        # 转换股票代码格式
        secids = []
        for code in stock_codes:
            if code.startswith('6'):
                secids.append(f"1.{code}")
            else:
                secids.append(f"0.{code}")

        url = "http://push2.eastmoney.com/api/qt/ulist.np/get"
        params = {
            'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
            'fltt': 2,
            'invt': 2,
            'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f26',
            'secids': ','.join(secids)
        }

        try:
            response = self.session.get(url, params=params, timeout=10)
            data = response.json()

            realtime_data = []
            if data['rc'] == 0 and 'data' in data and data['data']:
                stocks = data['data']['diff']

                for stock in stocks:
                    stock_info = {
                        'stock_code': stock['f12'],
                        'stock_name': stock['f14'],
                        'current_price': stock['f2'],
                        'change_percent': stock['f3'],
                        'change_amount': stock['f4'],
                        'volume': stock['f5'],
                        'amount': stock['f6'],
                        'open_price': stock['f17'],
                        'prev_close': stock['f18'],
                        'high_price': stock['f15'],
                        'low_price': stock['f16'],
                        'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    realtime_data.append(stock_info)

            return realtime_data

        except Exception as e:
            self.logger.error(f"获取实时数据失败: {e}")
            return []
    
    def get_market_data(self, stock_list: List[str], start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """获取多只股票数据"""
        market_data = {}
        for stock in stock_list:
            try:
                market_data[stock] = self.load_stock_data(stock, start_date, end_date)
                self.logger.info(f"成功加载股票 {stock} 数据")
            except Exception as e:
                self.logger.error(f"加载股票 {stock} 数据失败: {e}")
        
        return market_data

class FeatureEngineering:
    """特征工程模块"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.feature_importance = {}
    
    def create_technical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建技术分析特征"""
        data = df.copy()
        
        # 移动平均线
        data['ma_5'] = data['close'].rolling(window=5).mean()
        data['ma_10'] = data['close'].rolling(window=10).mean()
        data['ma_20'] = data['close'].rolling(window=20).mean()
        data['ma_60'] = data['close'].rolling(window=60).mean()
        
        # 价格位置
        data['price_position'] = (data['close'] - data['low'].rolling(20).min()) / (
            data['high'].rolling(20).max() - data['low'].rolling(20).min()
        )
        
        # RSI
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        data['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        exp1 = data['close'].ewm(span=12).mean()
        exp2 = data['close'].ewm(span=26).mean()
        data['macd'] = exp1 - exp2
        data['macd_signal'] = data['macd'].ewm(span=9).mean()
        data['macd_hist'] = data['macd'] - data['macd_signal']
        
        # 布林带
        data['bb_upper'] = data['ma_20'] + (data['close'].rolling(20).std() * 2)
        data['bb_lower'] = data['ma_20'] - (data['close'].rolling(20).std() * 2)
        data['bb_width'] = data['bb_upper'] - data['bb_lower']
        data['bb_position'] = (data['close'] - data['bb_lower']) / data['bb_width']
        
        # 成交量指标
        data['volume_ma'] = data['volume'].rolling(window=20).mean()
        data['volume_ratio'] = data['volume'] / data['volume_ma']
        
        # 波动率
        data['volatility'] = data['return'].rolling(window=20).std()
        
        # 动量指标
        data['momentum_5'] = data['close'] / data['close'].shift(5) - 1
        data['momentum_10'] = data['close'] / data['close'].shift(10) - 1
        data['momentum_20'] = data['close'] / data['close'].shift(20) - 1
        
        return data
    
    def create_fundamental_features(self, df: pd.DataFrame, stock_code: str = None) -> pd.DataFrame:
        """创建基本面特征 - 使用真实数据"""
        data = df.copy()

        if stock_code:
            try:
                # 获取估值数据
                data_manager = DataManager()
                valuation_data = data_manager.get_valuation_data(stock_code)

                if valuation_data:
                    # 添加估值特征
                    pe_ratio = valuation_data.get('pe_ratio') or 0
                    pb_ratio = valuation_data.get('pb_ratio') or 0
                    ps_ratio = valuation_data.get('ps_ratio') or 0

                    data['pe_ratio'] = pe_ratio
                    data['pb_ratio'] = pb_ratio
                    data['ps_ratio'] = ps_ratio

                    # 计算估值偏离度
                    data['pe_percentile'] = self._calculate_percentile(data, 'pe_ratio', pe_ratio)
                    data['pb_percentile'] = self._calculate_percentile(data, 'pb_ratio', pb_ratio)

                # 获取财务数据
                financial_data = data_manager.get_financial_data(stock_code, '2')  # 利润表
                if not financial_data.empty:
                    # 计算ROE、ROA等财务比率
                    data = self._add_financial_ratios(data, financial_data, stock_code)

            except Exception as e:
                print(f"获取基本面数据失败: {e}")

        return data

    def _calculate_percentile(self, data: pd.DataFrame, column: str, value: float) -> float:
        """计算百分位数"""
        if value <= 0:
            return 0.5  # 默认中位数
        return 0.5  # 简化处理，实际应该计算真实百分位数

    def _add_financial_ratios(self, data: pd.DataFrame, financial_data: pd.DataFrame, stock_code: str) -> pd.DataFrame:
        """添加财务比率特征"""
        # 这里可以根据财务数据计算各种比率
        # 示例：简化处理，实际应该根据具体财务指标计算
        data['roe'] = 0.1  # 净资产收益率
        data['roa'] = 0.05  # 总资产收益率
        data['gross_margin'] = 0.25  # 毛利率
        data['net_margin'] = 0.08  # 净利率

        return data
    
    def create_market_features(self, df: pd.DataFrame, market_index: pd.DataFrame = None) -> pd.DataFrame:
        """创建市场特征"""
        data = df.copy()
        
        if market_index is not None:
            # 与大盘的相关性
            data['market_corr'] = data['return'].rolling(60).corr(market_index['return'])
            
            # Beta系数
            market_returns = market_index['return'].rolling(60)
            stock_returns = data['return'].rolling(60)
            covariance = stock_returns.rolling(60).cov(market_returns)
            market_variance = market_returns.rolling(60).var()
            data['beta'] = covariance / market_variance
        
        return data
    
    def prepare_features(self, df: pd.DataFrame, target_days: int = 5, stock_code: str = None) -> Tuple[pd.DataFrame, pd.Series]:
        """准备训练特征和目标变量"""
        data = df.copy()

        # 创建各类特征
        data = self.create_technical_features(data)
        data = self.create_fundamental_features(data, stock_code)

        # 创建目标变量（未来N天的收益率）
        data['target'] = data['close'].shift(-target_days) / data['close'] - 1

        # 选择特征列（包含技术指标和基本面指标）
        feature_columns = [
            # 技术指标
            'ma_5', 'ma_10', 'ma_20', 'ma_60', 'price_position',
            'rsi', 'macd', 'macd_signal', 'macd_hist',
            'bb_position', 'bb_width', 'volume_ratio',
            'volatility', 'momentum_5', 'momentum_10', 'momentum_20'
        ]

        # 添加基本面特征（如果有的话）
        fundamental_features = []
        if stock_code:
            fundamental_features = [
                'pe_ratio', 'pb_ratio', 'ps_ratio',
                'pe_percentile', 'pb_percentile',
                'roe', 'roa', 'gross_margin', 'net_margin'
            ]

        # 检查哪些特征实际存在于数据中
        all_features = feature_columns + fundamental_features
        available_features = [col for col in all_features if col in data.columns]
        feature_columns = available_features
        
        # 删除缺失值
        data = data.dropna()
        
        if len(data) == 0:
            raise ValueError("数据预处理后无有效数据")
        
        X = data[feature_columns]
        y = data['target']
        
        return X, y

class AIModel:
    """AI模型模块"""
    
    def __init__(self, model_type: str = 'random_forest'):
        self.model_type = model_type
        self.model = None
        self.scaler = StandardScaler()
        self.is_fitted = False
        self.feature_importance = {}
    
    def _create_model(self):
        """创建模型"""
        if self.model_type == 'random_forest':
            return RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )
        elif self.model_type == 'gradient_boosting':
            return GradientBoostingRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42
            )
        elif self.model_type == 'linear':
            return Ridge(alpha=1.0)
        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}")
    
    def train(self, X: pd.DataFrame, y: pd.Series, test_size: float = 0.2):
        """训练模型"""
        # 时间序列分割
        tscv = TimeSeriesSplit(n_splits=5)
        
        # 数据标准化
        X_scaled = self.scaler.fit_transform(X)
        
        # 创建模型
        self.model = self._create_model()
        
        # 交叉验证评估
        cv_scores = []
        for train_idx, val_idx in tscv.split(X_scaled):
            X_train, X_val = X_scaled[train_idx], X_scaled[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            # 训练模型
            self.model.fit(X_train, y_train)
            
            # 预测和评估
            y_pred = self.model.predict(X_val)
            score = mean_squared_error(y_val, y_pred)
            cv_scores.append(score)
        
        # 在全部数据上训练最终模型
        self.model.fit(X_scaled, y)
        
        # 记录特征重要性
        if hasattr(self.model, 'feature_importances_'):
            self.feature_importance = dict(zip(X.columns, self.model.feature_importances_))
        
        self.is_fitted = True
        
        return {
            'cv_scores': cv_scores,
            'mean_cv_score': np.mean(cv_scores),
            'feature_importance': self.feature_importance
        }
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """预测"""
        if not self.is_fitted:
            raise ValueError("模型未训练，请先调用 train() 方法")
        
        X_scaled = self.scaler.transform(X)
        return self.model.predict(X_scaled)
    
    def save_model(self, filepath: str):
        """保存模型"""
        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'model_type': self.model_type,
            'feature_importance': self.feature_importance,
            'is_fitted': self.is_fitted
        }
        joblib.dump(model_data, filepath)
    
    def load_model(self, filepath: str):
        """加载模型"""
        model_data = joblib.load(filepath)
        self.model = model_data['model']
        self.scaler = model_data['scaler']
        self.model_type = model_data['model_type']
        self.feature_importance = model_data['feature_importance']
        self.is_fitted = model_data['is_fitted']

class TradingStrategy:
    """交易策略模块"""
    
    def __init__(self, model: AIModel, risk_manager=None):
        self.model = model
        self.risk_manager = risk_manager
        self.positions = {}
        self.cash = 1000000  # 初始资金100万
        self.transaction_cost = 0.002  # 交易成本0.2%
    
    def generate_signals(self, X: pd.DataFrame, threshold: float = 0.02) -> pd.Series:
        """生成交易信号"""
        predictions = self.model.predict(X)
        
        signals = pd.Series(index=X.index, data=0)  # 0: 持有, 1: 买入, -1: 卖出
        
        # 基于预测收益率生成信号
        signals[predictions > threshold] = 1   # 预测涨幅超过阈值，买入
        signals[predictions < -threshold] = -1 # 预测跌幅超过阈值，卖出
        
        return signals
    
    def calculate_position_size(self, signal: float, current_price: float, volatility: float = 0.02) -> float:
        """计算仓位大小"""
        if signal == 0:
            return 0
        
        # 基于波动率的仓位管理
        max_position_value = self.cash * 0.1  # 单只股票最大仓位10%
        
        # 根据波动率调整仓位
        volatility_adjusted_size = max_position_value * (0.02 / max(volatility, 0.005))
        
        return min(volatility_adjusted_size, max_position_value) / current_price
    
    def execute_strategy(self, data: pd.DataFrame, stock_code: str) -> Dict:
        """执行交易策略"""
        # 准备特征
        feature_eng = FeatureEngineering()
        X, _ = feature_eng.prepare_features(data, stock_code=stock_code)
        
        # 生成信号
        signals = self.generate_signals(X)
        
        # 执行交易
        trades = []
        portfolio_value = []
        current_position = 0
        
        for i, (date, signal) in enumerate(signals.items()):
            if i >= len(data):
                break
                
            current_price = data.iloc[i]['close']
            
            if signal != 0 and current_position == 0:  # 开仓
                position_size = self.calculate_position_size(signal, current_price)
                current_position = position_size * signal
                
                trade_value = abs(current_position) * current_price
                self.cash -= trade_value * (1 + self.transaction_cost)
                
                trades.append({
                    'date': date,
                    'action': 'buy' if signal > 0 else 'sell',
                    'price': current_price,
                    'quantity': abs(current_position),
                    'value': trade_value
                })
            
            elif signal == 0 and current_position != 0:  # 平仓
                trade_value = abs(current_position) * current_price
                self.cash += trade_value * (1 - self.transaction_cost)
                
                trades.append({
                    'date': date,
                    'action': 'close',
                    'price': current_price,
                    'quantity': abs(current_position),
                    'value': trade_value
                })
                
                current_position = 0
            
            # 计算当前组合价值
            position_value = current_position * current_price if current_position != 0 else 0
            total_value = self.cash + position_value
            portfolio_value.append(total_value)
        
        return {
            'trades': trades,
            'portfolio_value': portfolio_value,
            'final_cash': self.cash,
            'total_trades': len(trades)
        }

class BacktestEngine:
    """回测引擎"""
    
    def __init__(self):
        self.results = {}
    
    def run_backtest(self, strategy: TradingStrategy, data: Dict[str, pd.DataFrame]) -> Dict:
        """运行回测"""
        total_results = {
            'trades': [],
            'portfolio_values': [],
            'stock_performance': {}
        }
        
        for stock_code, stock_data in data.items():
            if len(stock_data) < 100:  # 确保有足够的数据
                continue
            
            print(f"回测股票: {stock_code}")
            
            try:
                result = strategy.execute_strategy(stock_data, stock_code)
                total_results['stock_performance'][stock_code] = result
                total_results['trades'].extend(result['trades'])
                total_results['portfolio_values'].extend(result['portfolio_value'])
                
            except Exception as e:
                print(f"回测股票 {stock_code} 失败: {e}")
        
        # 计算绩效指标
        total_results['performance_metrics'] = self.calculate_performance_metrics(
            total_results['portfolio_values']
        )
        
        return total_results
    
    def calculate_performance_metrics(self, portfolio_values: List[float]) -> Dict:
        """计算绩效指标"""
        if not portfolio_values:
            return {}
        
        values = np.array(portfolio_values)
        returns = np.diff(values) / values[:-1]
        
        metrics = {
            'total_return': (values[-1] - values[0]) / values[0],
            'annual_return': ((values[-1] / values[0]) ** (252 / len(values))) - 1,
            'volatility': np.std(returns) * np.sqrt(252),
            'sharpe_ratio': np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0,
            'max_drawdown': self.calculate_max_drawdown(values)
        }
        
        return metrics
    
    def calculate_max_drawdown(self, values: np.ndarray) -> float:
        """计算最大回撤"""
        peak = np.maximum.accumulate(values)
        drawdown = (values - peak) / peak
        return np.min(drawdown)

class QuantTradingSystem:
    """量化交易系统主类"""
    
    def __init__(self):
        self.data_manager = DataManager()
        self.feature_engineering = FeatureEngineering()
        self.model = None
        self.strategy = None
        self.backtest_engine = BacktestEngine()
    
    def initialize_system(self, stock_list: List[str], start_date: str, end_date: str):
        """初始化系统"""
        print("正在初始化量化交易系统...")
        
        # 1. 加载数据
        print("1. 加载股票数据...")
        self.market_data = self.data_manager.get_market_data(stock_list, start_date, end_date)
        
        # 2. 训练模型
        print("2. 训练AI模型...")
        self.train_model()
        
        # 3. 初始化策略
        print("3. 初始化交易策略...")
        self.strategy = TradingStrategy(self.model)
        
        print("系统初始化完成！")
    
    def train_model(self, model_type: str = 'random_forest'):
        """训练AI模型"""
        self.model = AIModel(model_type)
        
        # 合并所有股票数据进行训练
        all_X = []
        all_y = []
        
        for stock_code, data in self.market_data.items():
            try:
                X, y = self.feature_engineering.prepare_features(data, stock_code=stock_code)
                all_X.append(X)
                all_y.append(y)
            except Exception as e:
                print(f"处理股票 {stock_code} 数据失败: {e}")
        
        if not all_X:
            raise ValueError("没有有效的训练数据")
        
        # 合并数据
        combined_X = pd.concat(all_X, ignore_index=True)
        combined_y = pd.concat(all_y, ignore_index=True)
        
        # 训练模型
        training_results = self.model.train(combined_X, combined_y)
        print(f"模型训练完成，交叉验证得分: {training_results['mean_cv_score']:.4f}")
        
        return training_results
    
    def run_backtest(self) -> Dict:
        """运行回测"""
        print("开始回测...")
        results = self.backtest_engine.run_backtest(self.strategy, self.market_data)
        
        print("回测完成！")
        print("绩效指标:")
        for metric, value in results['performance_metrics'].items():
            print(f"  {metric}: {value:.4f}")
        
        return results
    
    def save_system(self, filepath: str):
        """保存系统"""
        self.model.save_model(f"{filepath}_model.joblib")
        
        system_config = {
            'market_data_summary': {k: len(v) for k, v in self.market_data.items()},
            'model_type': self.model.model_type,
            'feature_importance': self.model.feature_importance
        }
        
        with open(f"{filepath}_config.json", 'w', encoding='utf-8') as f:
            json.dump(system_config, f, ensure_ascii=False, indent=2, default=str)

class RealTimeTradingSystem:
    """实时交易系统"""

    def __init__(self, model_path: str = None):
        self.data_manager = DataManager()
        self.feature_engineering = FeatureEngineering()
        self.model = None
        self.is_running = False
        self.monitoring_stocks = []
        self.signal_threshold = 0.02

        # 加载已训练的模型
        if model_path:
            self.load_model(model_path)

    def load_model(self, model_path: str):
        """加载训练好的模型"""
        try:
            model_data = joblib.load(f"{model_path}_model.joblib")
            self.model = model_data['model']
            self.feature_engineering.scaler = model_data['scaler']
            print("实时交易模型加载成功！")
        except Exception as e:
            print(f"模型加载失败: {e}")

    def start_realtime_monitoring(self, stock_list: List[str], interval: int = 60):
        """开始实时监控"""
        if not self.model:
            print("请先加载训练好的模型！")
            return

        self.monitoring_stocks = stock_list
        self.is_running = True

        print(f"开始实时监控 {len(stock_list)} 只股票...")
        print(f"监控间隔: {interval} 秒")
        print("-" * 50)

        try:
            while self.is_running:
                self._process_realtime_data()
                time.sleep(interval)

        except KeyboardInterrupt:
            print("\n实时监控已停止")
        except Exception as e:
            print(f"实时监控出错: {e}")

    def stop_realtime_monitoring(self):
        """停止实时监控"""
        self.is_running = False
        print("实时监控已停止")

    def _process_realtime_data(self):
        """处理实时数据并生成信号"""
        try:
            # 获取实时数据
            realtime_data = self.data_manager.get_realtime_data(self.monitoring_stocks)

            if not realtime_data:
                print("无法获取实时数据")
                return

            signals = []

            for stock_data in realtime_data:
                stock_code = stock_data['stock_code']
                current_price = stock_data['current_price']
                change_percent = stock_data['change_percent']

                # 获取历史数据用于特征计算
                historical_data = self.data_manager.load_stock_data(
                    stock_code,
                    (datetime.now() - timedelta(days=200)).strftime('%Y-%m-%d'),
                    datetime.now().strftime('%Y-%m-%d')
                )

                if len(historical_data) < 50:
                    continue

                # 添加当前实时数据
                current_row = pd.DataFrame([{
                    'date': pd.Timestamp.now(),
                    'open': stock_data['open_price'],
                    'high': stock_data['high_price'],
                    'low': stock_data['low_price'],
                    'close': current_price,
                    'volume': stock_data['volume'],
                    'amount': stock_data['amount']
                }])

                # 合并历史数据和实时数据
                combined_data = pd.concat([historical_data, current_row], ignore_index=True)
                combined_data['return'] = combined_data['close'].pct_change()

                # 生成特征
                features = self.feature_engineering.prepare_features(
                    combined_data,
                    stock_code=stock_code
                )

                if len(features[0]) == 0:
                    continue

                # 获取最新特征
                latest_features = features[0].iloc[-1:].values

                # 生成预测
                try:
                    prediction = self.model.predict(latest_features)[0]

                    # 生成交易信号
                    signal = self._generate_signal(prediction, current_price, change_percent)

                    if signal != 0:
                        signals.append({
                            'stock_code': stock_code,
                            'stock_name': stock_data['stock_name'],
                            'signal': signal,
                            'current_price': current_price,
                            'change_percent': change_percent,
                            'prediction': prediction,
                            'timestamp': datetime.now().strftime('%H:%M:%S')
                        })

                except Exception as e:
                    print(f"预测股票 {stock_code} 时出错: {e}")

            # 显示交易信号
            if signals:
                self._display_signals(signals)

        except Exception as e:
            print(f"处理实时数据出错: {e}")

    def _generate_signal(self, prediction: float, current_price: float, change_percent: float) -> int:
        """生成交易信号"""
        if prediction > self.signal_threshold:
            return 1  # 买入信号
        elif prediction < -self.signal_threshold:
            return -1  # 卖出信号
        else:
            return 0  # 无信号

    def _display_signals(self, signals: List[Dict]):
        """显示交易信号"""
        print(f"\n📊 交易信号 ({datetime.now().strftime('%H:%M:%S')})")
        print("-" * 60)

        for signal in signals:
            signal_type = "🟢 买入" if signal['signal'] == 1 else "🔴 卖出"
            print(f"{signal_type} {signal['stock_code']} {signal['stock_name']}")
            print(".2f")
            print(".2f")
            print(f"   预测收益率: {signal['prediction']:.2%}")
            print()

    def get_realtime_quotes(self, stock_codes: List[str]) -> List[Dict]:
        """获取实时行情"""
        return self.data_manager.get_realtime_data(stock_codes)

    def set_signal_threshold(self, threshold: float):
        """设置信号阈值"""
        self.signal_threshold = threshold
        print(f"信号阈值已设置为: {threshold:.2%}")


def main():
    """主函数示例"""
    print("=== AI量化交易系统 ===\n")

    # 初始化系统
    quant_system = QuantTradingSystem()

    # 设置参数
    stock_list = ['000001', '000002', '600036', '600519', '000858']  # 示例股票
    start_date = '2020-01-01'
    end_date = '2023-12-31'

    try:
        # 初始化系统
        quant_system.initialize_system(stock_list, start_date, end_date)

        # 运行回测
        backtest_results = quant_system.run_backtest()

        # 保存系统
        quant_system.save_system('quant_system')

        print(f"\n总交易次数: {backtest_results.get('total_trades', 0)}")
        print("系统保存完成！")

        # 启动实时交易系统
        print("\n" + "="*50)
        print("启动实时交易系统...")
        realtime_system = RealTimeTradingSystem('quant_system')
        realtime_system.start_realtime_monitoring(stock_list, interval=30)  # 30秒间隔

    except Exception as e:
        print(f"系统运行出错: {e}")


def demo_realtime_quotes():
    """演示实时行情功能"""
    print("=== 实时行情演示 ===\n")

    data_manager = DataManager()
    stock_list = ['000001', '000002', '600036', '600519', '000858']

    print("获取实时行情...")
    realtime_data = data_manager.get_realtime_data(stock_list)

    if realtime_data:
        print(f"成功获取 {len(realtime_data)} 只股票实时数据:\n")
        print("-" * 70)
        print("股票代码  股票名称      当前价格    涨跌幅    涨跌额    成交量")
        print("-" * 70)

        for stock in realtime_data:
            print("8s")

        print("-" * 70)
    else:
        print("获取实时数据失败")


if __name__ == "__main__":
    # 选择运行模式
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == 'realtime':
        demo_realtime_quotes()
    else:
        main()
