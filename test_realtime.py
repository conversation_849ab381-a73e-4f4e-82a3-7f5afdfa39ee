#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时数据获取测试脚本
"""

from ai_quant_trading_system import DataManager

def test_realtime_quotes():
    """测试实时行情功能"""
    print("=== 实时行情测试 ===\n")

    data_manager = DataManager()
    stock_list = ['000001', '000002', '600036', '600519', '000858']

    print("获取实时行情...")
    realtime_data = data_manager.get_realtime_data(stock_list)

    if realtime_data:
        print(f"成功获取 {len(realtime_data)} 只股票实时数据:\n")
        print("-" * 80)
        print("股票代码  股票名称          当前价格    涨跌幅      涨跌额      成交量")
        print("-" * 80)

        for stock in realtime_data:
            print("8s")

        print("-" * 80)
        print("✅ 实时数据获取成功！")
    else:
        print("❌ 获取实时数据失败")

if __name__ == "__main__":
    test_realtime_quotes()
