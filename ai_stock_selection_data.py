import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import json
import warnings
warnings.filterwarnings('ignore')

class AIStockDataCollector:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'http://quote.eastmoney.com/',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def get_financial_data(self, stock_code, report_type='1'):
        """
        获取财务数据
        stock_code: 股票代码
        report_type: '1'=资产负债表, '2'=利润表, '3'=现金流量表
        """
        if stock_code.startswith('6'):
            market_code = f"SH{stock_code}"
        else:
            market_code = f"SZ{stock_code}"
            
        url = "http://f10.eastmoney.com/pc_hsgt/NewFinanceAnalysis/MainTargetAjax"
        params = {
            'companyType': '4',
            'reportDateType': '0',
            'reportType': report_type,
            'dates': '',
            'code': market_code
        }
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            data = response.json()
            
            financial_data = []
            if data and 'data' in data:
                dates = data['data']['dates']
                items = data['data']['data']
                
                for item in items:
                    item_name = item['name']
                    values = item['data']
                    
                    for i, date in enumerate(dates):
                        if i < len(values) and values[i] is not None:
                            financial_data.append({
                                '报告期': date,
                                '指标名称': item_name,
                                '数值': values[i],
                                '股票代码': stock_code
                            })
            
            return financial_data
            
        except Exception as e:
            print(f"获取财务数据失败: {e}")
            return []
    
    def get_technical_indicators(self, stock_code, period='101'):
        """
        计算技术指标
        """
        # 获取K线数据
        kline_data = self.get_stock_kline(stock_code, period, 200)
        if not kline_data:
            return []
        
        df = pd.DataFrame(kline_data)
        df['日期'] = pd.to_datetime(df['日期'])
        df = df.sort_values('日期')
        
        # 计算技术指标
        df['MA5'] = df['收盘价'].rolling(window=5).mean()
        df['MA10'] = df['收盘价'].rolling(window=10).mean()
        df['MA20'] = df['收盘价'].rolling(window=20).mean()
        df['MA60'] = df['收盘价'].rolling(window=60).mean()
        
        # RSI计算
        delta = df['收盘价'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        
        # 布林带
        df['BB_middle'] = df['收盘价'].rolling(window=20).mean()
        bb_std = df['收盘价'].rolling(window=20).std()
        df['BB_upper'] = df['BB_middle'] + (bb_std * 2)
        df['BB_lower'] = df['BB_middle'] - (bb_std * 2)
        
        # MACD
        exp1 = df['收盘价'].ewm(span=12).mean()
        exp2 = df['收盘价'].ewm(span=26).mean()
        df['MACD'] = exp1 - exp2
        df['MACD_signal'] = df['MACD'].ewm(span=9).mean()
        df['MACD_hist'] = df['MACD'] - df['MACD_signal']
        
        return df.to_dict('records')
    
    def get_fund_flow(self, stock_code):
        """
        获取资金流向数据
        """
        if stock_code.startswith('6'):
            secid = f"1.{stock_code}"
        else:
            secid = f"0.{stock_code}"
        
        url = "http://push2his.eastmoney.com/api/qt/stock/fflow/kline/get"
        params = {
            'secid': secid,
            'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
            'fields1': 'f1,f2,f3,f7',
            'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63',
            'klt': 101,
            'lmt': 30
        }
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            data = response.json()
            
            fund_flow_data = []
            if data['rc'] == 0 and 'data' in data and data['data']:
                klines = data['data']['klines']
                
                for kline in klines:
                    items = kline.split(',')
                    flow_info = {
                        '日期': items[0],
                        '主力净流入': float(items[1]) if items[1] != '-' else 0,
                        '小单净流入': float(items[2]) if items[2] != '-' else 0,
                        '中单净流入': float(items[3]) if items[3] != '-' else 0,
                        '大单净流入': float(items[4]) if items[4] != '-' else 0,
                        '超大单净流入': float(items[5]) if items[5] != '-' else 0,
                        '股票代码': stock_code
                    }
                    fund_flow_data.append(flow_info)
                
                return fund_flow_data
            
        except Exception as e:
            print(f"获取资金流向数据失败: {e}")
            return []
    
    def get_valuation_data(self, stock_code):
        """
        获取估值数据
        """
        if stock_code.startswith('6'):
            secid = f"1.{stock_code}"
        else:
            secid = f"0.{stock_code}"
        
        url = "http://push2.eastmoney.com/api/qt/stock/get"
        params = {
            'secid': secid,
            'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
            'fields': 'f43,f57,f58,f162,f92,f173,f104,f105,f84,f85,f183,f184,f185,f186,f187,f188'
        }
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            data = response.json()
            
            if data['rc'] == 0 and 'data' in data:
                stock_data = data['data']
                valuation_info = {
                    '股票代码': stock_code,
                    '市盈率TTM': stock_data.get('f162'),
                    '市净率': stock_data.get('f173'),
                    '市销率': stock_data.get('f184'),
                    '市现率': stock_data.get('f185'),
                    'EV/EBITDA': stock_data.get('f186'),
                    '股息率': stock_data.get('f187'),
                    '股息收益率': stock_data.get('f188'),
                    '总市值': stock_data.get('f43'),
                    '流通市值': stock_data.get('f85'),
                    '总股本': stock_data.get('f84'),
                    '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                return valuation_info
            
        except Exception as e:
            print(f"获取估值数据失败: {e}")
            return {}
    
    def get_industry_data(self, stock_code):
        """
        获取行业数据
        """
        url = f"http://f10.eastmoney.com/pc_hsgt/NewFinanceAnalysis/CompanyInfoAjax"
        params = {
            'code': f'SH{stock_code}' if stock_code.startswith('6') else f'SZ{stock_code}'
        }
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            data = response.json()
            
            industry_info = {
                '股票代码': stock_code,
                '所属行业': data.get('industry', ''),
                '行业代码': data.get('industry_code', ''),
                '所属概念': data.get('concept', ''),
                '主营业务': data.get('main_business', ''),
                '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            return industry_info
            
        except Exception as e:
            print(f"获取行业数据失败: {e}")
            return {}
    
    def get_stock_kline(self, stock_code, period='101', count=100):
        """获取K线数据 - 从之前的代码复用"""
        if stock_code.startswith('6'):
            stock_code = f"1.{stock_code}"
        else:
            stock_code = f"0.{stock_code}"
        
        url = "http://push2his.eastmoney.com/api/qt/stock/kline/get"
        params = {
            'secid': stock_code,
            'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
            'fields1': 'f1,f2,f3,f4,f5,f6',
            'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
            'klt': period,
            'fqt': 1,
            'end': '20500101',
            'lmt': count
        }
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            data = response.json()
            
            if data['rc'] == 0 and 'data' in data and data['data']:
                klines = data['data']['klines']
                kline_data = []
                
                for kline in klines:
                    items = kline.split(',')
                    kline_info = {
                        '日期': items[0],
                        '开盘价': float(items[1]),
                        '收盘价': float(items[2]),
                        '最高价': float(items[3]),
                        '最低价': float(items[4]),
                        '成交量': int(items[5]),
                        '成交额': float(items[6]),
                        '振幅': float(items[7]),
                        '涨跌幅': float(items[8]),
                        '涨跌额': float(items[9]),
                        '换手率': float(items[10])
                    }
                    kline_data.append(kline_info)
                
                return kline_data
            return []
            
        except Exception as e:
            print(f"获取K线数据出错: {e}")
            return []
    
    def collect_comprehensive_data(self, stock_code):
        """
        综合收集一只股票的所有数据
        """
        print(f"正在收集股票 {stock_code} 的综合数据...")
        
        comprehensive_data = {
            'stock_code': stock_code,
            'collection_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'kline_data': [],
            'technical_indicators': [],
            'financial_data': [],
            'fund_flow': [],
            'valuation': {},
            'industry': {}
        }
        
        # 1. K线数据
        print("  - 获取K线数据...")
        comprehensive_data['kline_data'] = self.get_stock_kline(stock_code, count=100)
        time.sleep(0.5)
        
        # 2. 技术指标
        print("  - 计算技术指标...")
        comprehensive_data['technical_indicators'] = self.get_technical_indicators(stock_code)
        time.sleep(0.5)
        
        # 3. 财务数据（资产负债表、利润表、现金流量表）
        print("  - 获取财务数据...")
        for report_type in ['1', '2', '3']:
            financial_data = self.get_financial_data(stock_code, report_type)
            comprehensive_data['financial_data'].extend(financial_data)
            time.sleep(0.5)
        
        # 4. 资金流向
        print("  - 获取资金流向...")
        comprehensive_data['fund_flow'] = self.get_fund_flow(stock_code)
        time.sleep(0.5)
        
        # 5. 估值数据
        print("  - 获取估值数据...")
        comprehensive_data['valuation'] = self.get_valuation_data(stock_code)
        time.sleep(0.5)
        
        # 6. 行业数据
        print("  - 获取行业数据...")
        comprehensive_data['industry'] = self.get_industry_data(stock_code)
        time.sleep(0.5)
        
        return comprehensive_data
    
    def save_comprehensive_data(self, data, filename=None):
        """保存综合数据"""
        if filename is None:
            filename = f"{data['stock_code']}_comprehensive_data.json"
        
        import os
        if not os.path.exists('data'):
            os.makedirs('data')
        
        filepath = os.path.join('data', filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"综合数据已保存到: {filepath}")
        return filepath

# AI特征工程类
class AIFeatureEngineering:
    @staticmethod
    def calculate_financial_ratios(financial_data):
        """计算财务比率特征"""
        features = {}
        
        # 这里需要根据具体的财务数据结构来计算各种比率
        # 示例特征
        features['debt_to_equity'] = 0  # 负债权益比
        features['current_ratio'] = 0   # 流动比率
        features['roa'] = 0            # 资产收益率
        features['roe'] = 0            # 净资产收益率
        features['gross_margin'] = 0   # 毛利率
        features['net_margin'] = 0     # 净利率
        
        return features
    
    @staticmethod
    def calculate_technical_features(kline_data):
        """计算技术分析特征"""
        if not kline_data:
            return {}
        
        df = pd.DataFrame(kline_data)
        features = {}
        
        # 价格动量特征
        features['price_momentum_5d'] = (df['收盘价'].iloc[-1] - df['收盘价'].iloc[-6]) / df['收盘价'].iloc[-6] if len(df) > 5 else 0
        features['price_momentum_20d'] = (df['收盘价'].iloc[-1] - df['收盘价'].iloc[-21]) / df['收盘价'].iloc[-21] if len(df) > 20 else 0
        
        # 成交量特征
        features['volume_ratio'] = df['成交量'].rolling(5).mean().iloc[-1] / df['成交量'].rolling(20).mean().iloc[-1] if len(df) > 20 else 1
        
        # 波动率特征
        features['volatility'] = df['涨跌幅'].std()
        
        return features
    
    @staticmethod
    def create_feature_vector(comprehensive_data):
        """创建用于AI模型的特征向量"""
        features = {}
        
        # 技术指标特征
        tech_features = AIFeatureEngineering.calculate_technical_features(
            comprehensive_data['kline_data']
        )
        features.update(tech_features)
        
        # 财务比率特征
        financial_features = AIFeatureEngineering.calculate_financial_ratios(
            comprehensive_data['financial_data']
        )
        features.update(financial_features)
        
        # 估值特征
        valuation = comprehensive_data.get('valuation', {})
        features['pe_ratio'] = valuation.get('市盈率TTM', 0) or 0
        features['pb_ratio'] = valuation.get('市净率', 0) or 0
        features['ps_ratio'] = valuation.get('市销率', 0) or 0
        
        # 资金流向特征
        fund_flow = comprehensive_data.get('fund_flow', [])
        if fund_flow:
            recent_flow = fund_flow[-5:]  # 最近5天
            features['main_fund_flow_5d'] = sum([f['主力净流入'] for f in recent_flow])
        
        return features

def main():
    """主函数示例"""
    collector = AIStockDataCollector()
    
    # 示例：收集平安银行的综合数据
    stock_code = '000001'
    comprehensive_data = collector.collect_comprehensive_data(stock_code)
    
    # 保存数据
    collector.save_comprehensive_data(comprehensive_data)
    
    # 创建AI特征
    features = AIFeatureEngineering.create_feature_vector(comprehensive_data)
    print(f"\n生成的AI特征数量: {len(features)}")
    print("特征示例:")
    for key, value in list(features.items())[:10]:
        print(f"  {key}: {value}")

if __name__ == "__main__":
    main()
