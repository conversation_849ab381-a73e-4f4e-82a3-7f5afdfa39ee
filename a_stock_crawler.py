import requests
import pandas as pd
import json
import time
from datetime import datetime, timedelta
import os

class AStockCrawler:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'http://quote.eastmoney.com/',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def get_stock_list(self, market='A'):
        """
        获取A股股票列表
        market: 'A' 表示A股全部股票
        """
        url = "http://80.push2.eastmoney.com/api/qt/clist/get"
        params = {
            'pn': 1,
            'pz': 5000,
            'po': 1,
            'np': 1,
            'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
            'fltt': 2,
            'invt': 2,
            'fid': 'f3',
            'fs': 'm:0 t:6,m:0 t:80,m:1 t:2,m:1 t:23',  # A股股票
            'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152'
        }
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            data = response.json()
            
            if data['rc'] == 0 and 'data' in data and data['data']:
                stocks = data['data']['diff']
                stock_list = []
                
                for stock in stocks:
                    stock_info = {
                        '股票代码': stock['f12'],
                        '股票名称': stock['f14'],
                        '最新价': stock['f2'],
                        '涨跌幅': stock['f3'],
                        '涨跌额': stock['f4'],
                        '成交量': stock['f5'],
                        '成交额': stock['f6'],
                        '振幅': stock['f7'],
                        '最高价': stock['f15'],
                        '最低价': stock['f16'],
                        '今开价': stock['f17'],
                        '昨收价': stock['f18'],
                        '市盈率': stock['f9'],
                        '市净率': stock['f23'],
                        '总市值': stock['f20'],
                        '流通市值': stock['f21']
                    }
                    stock_list.append(stock_info)
                
                return stock_list
            else:
                print("获取股票列表失败")
                return []
                
        except Exception as e:
            print(f"获取股票列表出错: {e}")
            return []
    
    def get_stock_kline(self, stock_code, period='101', count=100):
        """
        获取股票K线数据
        stock_code: 股票代码 (如 '000001')
        period: 周期 ('101'=日K, '102'=周K, '103'=月K)
        count: 获取数据条数
        """
        # 判断股票所属市场
        if stock_code.startswith('6'):
            stock_code = f"1.{stock_code}"  # 上海
        else:
            stock_code = f"0.{stock_code}"  # 深圳
        
        url = "http://push2his.eastmoney.com/api/qt/stock/kline/get"
        params = {
            'secid': stock_code,
            'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
            'fields1': 'f1,f2,f3,f4,f5,f6',
            'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
            'klt': period,
            'fqt': 1,
            'end': '20500101',
            'lmt': count
        }
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            data = response.json()
            
            if data['rc'] == 0 and 'data' in data and data['data']:
                klines = data['data']['klines']
                kline_data = []
                
                for kline in klines:
                    items = kline.split(',')
                    kline_info = {
                        '日期': items[0],
                        '开盘价': float(items[1]),
                        '收盘价': float(items[2]),
                        '最高价': float(items[3]),
                        '最低价': float(items[4]),
                        '成交量': int(items[5]),
                        '成交额': float(items[6]),
                        '振幅': float(items[7]),
                        '涨跌幅': float(items[8]),
                        '涨跌额': float(items[9]),
                        '换手率': float(items[10])
                    }
                    kline_data.append(kline_info)
                
                return kline_data
            else:
                print(f"获取K线数据失败: {stock_code}")
                return []
                
        except Exception as e:
            print(f"获取K线数据出错: {e}")
            return []
    
    def get_stock_realtime(self, stock_codes):
        """
        获取股票实时数据
        stock_codes: 股票代码列表
        """
        if isinstance(stock_codes, str):
            stock_codes = [stock_codes]
        
        # 转换股票代码格式
        secids = []
        for code in stock_codes:
            if code.startswith('6'):
                secids.append(f"1.{code}")
            else:
                secids.append(f"0.{code}")
        
        url = "http://push2.eastmoney.com/api/qt/ulist.np/get"
        params = {
            'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
            'fltt': 2,
            'invt': 2,
            'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f26',
            'secids': ','.join(secids)
        }
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            data = response.json()
            
            if data['rc'] == 0 and 'data' in data and data['data']:
                stocks = data['data']['diff']
                realtime_data = []
                
                for stock in stocks:
                    stock_info = {
                        '股票代码': stock['f12'],
                        '股票名称': stock['f14'],
                        '最新价': stock['f2'],
                        '涨跌幅': stock['f3'],
                        '涨跌额': stock['f4'],
                        '成交量': stock['f5'],
                        '成交额': stock['f6'],
                        '今开价': stock['f17'],
                        '昨收价': stock['f18'],
                        '最高价': stock['f15'],
                        '最低价': stock['f16'],
                        '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    realtime_data.append(stock_info)
                
                return realtime_data
            else:
                print("获取实时数据失败")
                return []
                
        except Exception as e:
            print(f"获取实时数据出错: {e}")
            return []
    
    def save_to_csv(self, data, filename):
        """保存数据到CSV文件"""
        if not data:
            print("没有数据可保存")
            return
        
        df = pd.DataFrame(data)
        
        # 创建data目录
        if not os.path.exists('data'):
            os.makedirs('data')
        
        filepath = os.path.join('data', filename)
        df.to_csv(filepath, index=False, encoding='utf-8-sig')
        print(f"数据已保存到: {filepath}")
        return filepath

def main():
    """主函数示例"""
    crawler = AStockCrawler()
    
    print("=== A股股票数据爬虫 ===\n")
    
    # 1. 获取股票列表
    print("正在获取A股股票列表...")
    stock_list = crawler.get_stock_list()
    if stock_list:
        print(f"成功获取 {len(stock_list)} 只股票信息")
        crawler.save_to_csv(stock_list, 'a_stock_list.csv')
        
        # 显示前5只股票
        print("\n前5只股票信息:")
        for i, stock in enumerate(stock_list[:5]):
            print(f"{i+1}. {stock['股票代码']} {stock['股票名称']} - 最新价: {stock['最新价']}")
    
    # 2. 获取特定股票的K线数据
    print("\n" + "="*50)
    print("正在获取平安银行(000001)的K线数据...")
    kline_data = crawler.get_stock_kline('000001', period='101', count=30)
    if kline_data:
        print(f"成功获取 {len(kline_data)} 条K线数据")
        crawler.save_to_csv(kline_data, '000001_kline.csv')
        
        # 显示最新5条数据
        print("\n最新5条K线数据:")
        for i, kline in enumerate(kline_data[-5:]):
            print(f"{kline['日期']}: 开盘{kline['开盘价']} 收盘{kline['收盘价']} 涨跌幅{kline['涨跌幅']}%")
    
    # 3. 获取实时数据
    print("\n" + "="*50)
    print("正在获取热门股票实时数据...")
    hot_stocks = ['000001', '000002', '600036', '600519', '000858']
    realtime_data = crawler.get_stock_realtime(hot_stocks)
    if realtime_data:
        print(f"成功获取 {len(realtime_data)} 只股票实时数据")
        crawler.save_to_csv(realtime_data, 'realtime_data.csv')
        
        print("\n实时数据:")
        for stock in realtime_data:
            print(f"{stock['股票代码']} {stock['股票名称']}: {stock['最新价']} ({stock['涨跌幅']}%)")
    
    print(f"\n所有数据已保存到 data 目录下")

if __name__ == "__main__":
    main()
