#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速实时监控测试脚本
"""

from ai_quant_trading_system import DataManager, RealTimeTradingSystem
import time

def quick_realtime_test():
    """快速测试实时功能"""
    print("🚀 快速实时功能测试\n")

    # 1. 测试实时行情获取
    print("1️⃣ 测试实时行情获取...")
    data_manager = DataManager()
    stock_list = ['000001', '000002', '600036']

    realtime_data = data_manager.get_realtime_data(stock_list)
    if realtime_data:
        print("✅ 实时行情获取成功")
        for stock in realtime_data[:2]:  # 显示前2只股票
            print(".2f")
    else:
        print("❌ 实时行情获取失败")
        return

    print("\n" + "="*50)

    # 2. 测试实时交易信号
    print("2️⃣ 测试实时交易信号...")
    try:
        realtime_system = RealTimeTradingSystem('quant_system')

        if realtime_system.model:
            print("✅ AI模型加载成功")

            # 运行5秒的快速测试
            print("开始5秒实时监控测试...")

            def test_monitoring():
                try:
                    realtime_system.start_realtime_monitoring(stock_list, interval=3)
                except KeyboardInterrupt:
                    pass

            import threading
            monitor_thread = threading.Thread(target=test_monitoring, daemon=True)
            monitor_thread.start()

            time.sleep(5)  # 运行5秒
            realtime_system.stop_realtime_monitoring()

            print("✅ 实时监控测试完成")
        else:
            print("❌ AI模型未找到，请先运行主程序训练模型")

    except Exception as e:
        print(f"❌ 实时交易信号测试出错: {e}")

    print("\n" + "="*50)
    print("🎯 测试完成！")

if __name__ == "__main__":
    quick_realtime_test()
