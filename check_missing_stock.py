#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查缺失的股票并验证数据完整性
"""

import requests
import pandas as pd
import time
from datetime import datetime

class StockChecker:
    """股票检查器"""

    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'http://quote.eastmoney.com/',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)

    def check_stock_exists(self, stock_code):
        """检查股票是否存在"""
        print(f"正在检查股票 {stock_code} 是否存在...")

        # 转换股票代码格式
        if stock_code.startswith('6'):
            secid = f"1.{stock_code}"  # 上海
        else:
            secid = f"0.{stock_code}"  # 深圳

        url = "http://push2.eastmoney.com/api/qt/stock/get"
        params = {
            'secid': secid,
            'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
            'fields': 'f43,f57,f58,f162,f92,f173,f104,f105,f84,f85,f183,f184,f185,f186,f187,f188'
        }

        try:
            response = self.session.get(url, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data['rc'] == 0 and 'data' in data:
                    stock_data = data['data']
                    print("✅ 股票存在！")
                    print(f"   股票代码: {stock_code}")
                    print(f"   最新价格: {stock_data.get('f2', 'N/A')}")
                    print(f"   涨跌幅: {stock_data.get('f3', 'N/A')}%")
                    return True
                else:
                    print("❌ 股票不存在或API返回错误")
                    return False
            else:
                print(f"❌ HTTP请求失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ 检查股票时出错: {e}")
            return False

    def check_data_completeness(self):
        """检查数据完整性"""
        print("\n" + "="*50)
        print("📊 数据完整性检查")
        print("="*50)

        try:
            # 读取现有数据
            df = pd.read_csv('data/all_a_stocks.csv', dtype={'股票代码': str})
            print(f"现有数据: {len(df)} 只股票")
            print(f"唯一股票代码: {df['股票代码'].nunique()}")
            print(f"数据更新时间: {df['更新时间'].max()}")

            # 按市场统计
            market_stats = df['市场类型'].value_counts()
            print(f"\n市场分布:")
            for market, count in market_stats.items():
                print(f"  {market}: {count}只")

            # 检查常见股票代码段
            code_prefixes = ['000', '002', '300', '600', '601', '603', '688']
            print(f"\n各段代码统计:")
            for prefix in code_prefixes:
                count = len(df[df['股票代码'].str.startswith(prefix)])
                print(f"  {prefix}开头: {count}只")

        except FileNotFoundError:
            print("❌ 未找到数据文件")

def main():
    """主函数"""
    print("=== 股票数据检查工具 ===\n")

    checker = StockChecker()

    # 检查长沙银行
    print("🔍 检查长沙银行(601577)...")
    checker.check_stock_exists('601577')

    # 检查数据完整性
    checker.check_data_completeness()

    print("\n" + "="*50)
    print("💡 可能的原因:")
    print("1. API返回数据不完整（东方财富的限制）")
    print("2. 股票在非交易时间或特殊状态")
    print("3. 数据获取时的网络或API问题")
    print("4. 建议使用多种数据源进行补充")

if __name__ == "__main__":
    main()
