#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时交易系统演示脚本
"""

from ai_quant_trading_system import RealTimeTradingSystem, DataManager
import time
import threading

def demo_realtime_quotes():
    """演示实时行情功能"""
    print("=== 实时行情演示 ===\n")

    data_manager = DataManager()
    stock_list = ['000001', '000002', '600036', '600519', '000858']

    print("获取实时行情...")
    realtime_data = data_manager.get_realtime_data(stock_list)

    if realtime_data:
        print(f"成功获取 {len(realtime_data)} 只股票实时数据:\n")
        print("-" * 85)
        print("股票代码  股票名称          当前价格    涨跌幅      涨跌额      成交量(手)")
        print("-" * 85)

        for stock in realtime_data:
            print("8s")

        print("-" * 85)
        print("✅ 实时数据获取成功！")
    else:
        print("❌ 获取实时数据失败")

def demo_realtime_trading():
    """演示实时交易信号生成"""
    print("=== 实时交易信号演示 ===\n")

    # 初始化实时交易系统
    realtime_system = RealTimeTradingSystem('quant_system')

    if not realtime_system.model:
        print("❌ 未找到训练好的模型，请先运行主程序进行模型训练")
        return

    stock_list = ['000001', '000002', '600036', '600519', '000858']

    print("开始实时监控（运行10秒）...")
    print("按 Ctrl+C 停止监控\n")

    # 创建监控线程
    def monitoring_thread():
        try:
            realtime_system.start_realtime_monitoring(stock_list, interval=5)  # 5秒间隔
        except KeyboardInterrupt:
            realtime_system.stop_realtime_monitoring()

    # 启动监控线程
    monitor_thread = threading.Thread(target=monitoring_thread, daemon=True)
    monitor_thread.start()

    # 运行10秒后自动停止
    time.sleep(10)
    realtime_system.stop_realtime_monitoring()

    print("\n🎯 实时监控演示完成！")

def demo_realtime_analysis():
    """演示实时数据分析"""
    print("=== 实时数据分析演示 ===\n")

    data_manager = DataManager()
    stock_list = ['000001', '000002', '600036']

    print("连续获取实时数据进行分析...")

    for i in range(3):
        print(f"\n第 {i+1} 次数据获取:")
        realtime_data = data_manager.get_realtime_data(stock_list)

        if realtime_data:
            # 分析涨跌幅
            up_count = sum(1 for stock in realtime_data if stock['change_percent'] > 0)
            down_count = sum(1 for stock in realtime_data if stock['change_percent'] < 0)

            print(f"  📈 上涨: {up_count} 只")
            print(f"  📉 下跌: {down_count} 只")
            print(f"  ➡️  平盘: {len(realtime_data) - up_count - down_count} 只")

            # 显示价格最高的股票
            if realtime_data:
                highest = max(realtime_data, key=lambda x: x['current_price'])
                print(f"  🔥 价格最高: {highest['stock_code']} {highest['stock_name']} - ¥{highest['current_price']}")

        time.sleep(2)

    print("\n✅ 实时数据分析完成！")

def main():
    """主演示函数"""
    print("🚀 AI量化交易系统 - 实时功能演示\n")

    while True:
        print("\n请选择演示功能:")
        print("1. 实时行情获取")
        print("2. 实时交易信号")
        print("3. 实时数据分析")
        print("4. 退出演示")

        choice = input("\n请输入选择 (1-4): ").strip()

        if choice == '1':
            demo_realtime_quotes()
        elif choice == '2':
            demo_realtime_trading()
        elif choice == '3':
            demo_realtime_analysis()
        elif choice == '4':
            print("👋 感谢使用，再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
