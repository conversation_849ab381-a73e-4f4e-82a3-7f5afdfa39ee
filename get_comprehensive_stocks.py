#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面获取A股股票数据 - 多种策略确保完整性
"""

import requests
import pandas as pd
import json
import time
import os
from datetime import datetime
import logging
from concurrent.futures import ThreadPoolExecutor

class ComprehensiveStockCrawler:
    """全面股票爬虫"""

    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'http://quote.eastmoney.com/',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('comprehensive_crawler.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def get_comprehensive_stocks(self):
        """全面获取股票数据"""
        self.logger.info("开始全面获取A股股票数据...")

        all_stocks = []

        # 策略1: 使用不同的市场参数
        self.logger.info("策略1: 使用不同市场参数获取...")
        stocks_strategy1 = self._get_by_market_segments()
        all_stocks.extend(stocks_strategy1)

        # 策略2: 直接获取所有A股
        self.logger.info("策略2: 直接获取所有A股...")
        stocks_strategy2 = self._get_all_a_stocks()
        all_stocks.extend(stocks_strategy2)

        # 策略3: 分板块获取
        self.logger.info("策略3: 分板块获取...")
        stocks_strategy3 = self._get_by_boards()
        all_stocks.extend(stocks_strategy3)

        # 去重和清理
        final_stocks = self._deduplicate_and_clean(all_stocks)

        self.logger.info(f"全面获取完成，共 {len(final_stocks)} 只股票")
        return final_stocks

    def _get_by_market_segments(self):
        """按市场细分获取"""
        market_configs = [
            {'name': '上海主板', 'fs': 'm:1 t:2,m:1 t:23', 'exchange': '上海证券交易所'},
            {'name': '深圳主板', 'fs': 'm:0 t:6,m:0 t:80', 'exchange': '深圳证券交易所'},
            {'name': '中小板', 'fs': 'm:0 t:13', 'exchange': '深圳证券交易所'},
            {'name': '创业板', 'fs': 'm:0 t:80', 'exchange': '深圳证券交易所'},
            {'name': '科创板', 'fs': 'm:1 t:23', 'exchange': '上海证券交易所'},
        ]

        all_stocks = []
        for config in market_configs:
            try:
                stocks = self._get_market_stocks(config, max_pages=15)
                all_stocks.extend(stocks)
                self.logger.info(f"{config['name']}: 获取到 {len(stocks)} 只股票")
            except Exception as e:
                self.logger.error(f"获取 {config['name']} 失败: {e}")

        return all_stocks

    def _get_all_a_stocks(self):
        """直接获取所有A股"""
        try:
            url = "http://80.push2.eastmoney.com/api/qt/clist/get"
            params = {
                'pn': 1,
                'pz': 5000,  # 一次性获取更多
                'po': 1,
                'np': 1,
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': 2,
                'invt': 2,
                'fid': 'f3',
                'fs': 'm:0 t:6,m:0 t:80,m:1 t:2,m:1 t:23',  # 所有A股
                'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152'
            }

            response = self.session.get(url, params=params, timeout=30)
            data = response.json()

            stocks = []
            if data['rc'] == 0 and 'data' in data and 'diff' in data['data']:
                for stock in data['data']['diff']:
                    try:
                        stock_info = self._parse_stock(stock)
                        if stock_info:
                            stocks.append(stock_info)
                    except Exception as e:
                        continue

            return stocks

        except Exception as e:
            self.logger.error(f"直接获取所有A股失败: {e}")
            return []

    def _get_by_boards(self):
        """按板块获取"""
        boards = [
            {'name': '银行板块', 'fs': 'b:BK0475'},
            {'name': '券商板块', 'fs': 'b:BK0473'},
            {'name': '保险板块', 'fs': 'b:BK0474'},
            {'name': '房地产', 'fs': 'b:BK0451'},
            {'name': '医药生物', 'fs': 'b:BK0420'},
            {'name': '电子元件', 'fs': 'b:BK0449'},
        ]

        all_stocks = []
        for board in boards:
            try:
                stocks = self._get_board_stocks(board)
                all_stocks.extend(stocks)
            except Exception as e:
                self.logger.error(f"获取 {board['name']} 失败: {e}")

        return all_stocks

    def _get_market_stocks(self, config, max_pages=15):
        """获取特定市场的股票"""
        stocks = []

        for page in range(1, max_pages + 1):
            try:
                url = "http://80.push2.eastmoney.com/api/qt/clist/get"
                params = {
                    'pn': page,
                    'pz': 200,
                    'po': 1,
                    'np': 1,
                    'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                    'fltt': 2,
                    'invt': 2,
                    'fid': 'f3',
                    'fs': config['fs'],
                    'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152'
                }

                response = self.session.get(url, params=params, timeout=20)
                data = response.json()

                if data['rc'] != 0 or 'data' not in data or 'diff' not in data['data']:
                    break

                page_stocks = data['data']['diff']
                if not page_stocks:
                    break

                for stock in page_stocks:
                    try:
                        stock_info = self._parse_stock(stock, config.get('exchange'))
                        if stock_info:
                            stocks.append(stock_info)
                    except Exception as e:
                        continue

                if len(page_stocks) < 200:
                    break

                time.sleep(0.5)

            except Exception as e:
                self.logger.error(f"获取第 {page} 页失败: {e}")
                break

        return stocks

    def _get_board_stocks(self, board_config):
        """获取板块股票"""
        try:
            url = "http://80.push2.eastmoney.com/api/qt/clist/get"
            params = {
                'pn': 1,
                'pz': 100,
                'po': 1,
                'np': 1,
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': 2,
                'invt': 2,
                'fid': 'f3',
                'fs': board_config['fs'],
                'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152'
            }

            response = self.session.get(url, params=params, timeout=20)
            data = response.json()

            stocks = []
            if data['rc'] == 0 and 'data' in data and 'diff' in data['data']:
                for stock in data['data']['diff']:
                    try:
                        stock_info = self._parse_stock(stock)
                        if stock_info:
                            stocks.append(stock_info)
                    except Exception as e:
                        continue

            return stocks

        except Exception as e:
            self.logger.error(f"获取 {board_config['name']} 失败: {e}")
            return []

    def _parse_stock(self, stock, exchange=None):
        """解析股票数据"""
        try:
            stock_code = stock['f12']

            # 确定交易所
            if not exchange:
                if stock_code.startswith('6'):
                    exchange = '上海证券交易所'
                elif stock_code.startswith(('0', '3')):
                    exchange = '深圳证券交易所'
                else:
                    exchange = '其他'

            # 确定市场类型
            if stock_code.startswith('6'):
                if stock_code.startswith('688'):
                    market_type = '科创板'
                elif stock_code.startswith('601') or stock_code.startswith('600') or stock_code.startswith('603'):
                    market_type = '上海主板'
                else:
                    market_type = '上海主板'
            elif stock_code.startswith('000'):
                market_type = '深圳主板'
            elif stock_code.startswith('002'):
                market_type = '深圳中小板'
            elif stock_code.startswith('300'):
                market_type = '深圳创业板'
            else:
                market_type = '其他'

            stock_info = {
                '股票代码': stock_code,
                '股票名称': stock['f14'],
                '最新价': stock.get('f2', 0),
                '涨跌幅': stock.get('f3', 0),
                '涨跌额': stock.get('f4', 0),
                '成交量': stock.get('f5', 0),
                '成交额': stock.get('f6', 0),
                '振幅': stock.get('f7', 0),
                '最高价': stock.get('f15', 0),
                '最低价': stock.get('f16', 0),
                '今开价': stock.get('f17', 0),
                '昨收价': stock.get('f18', 0),
                '量比': stock.get('f10', 0),
                '换手率': stock.get('f8', 0),
                '市盈率': stock.get('f9', 0),
                '市净率': stock.get('f23', 0),
                '总市值': stock.get('f20', 0),
                '流通市值': stock.get('f21', 0),
                '总股本': stock.get('f22', 0),
                '流通股本': stock.get('f25', 0),
                '市场类型': market_type,
                '交易所': exchange,
                '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            return stock_info

        except Exception as e:
            return None

    def _deduplicate_and_clean(self, stocks):
        """去重和清理"""
        if not stocks:
            return []

        df = pd.DataFrame(stocks)
        initial_count = len(df)

        # 去重
        df = df.drop_duplicates(subset=['股票代码'], keep='last')
        self.logger.info(f"去重: {initial_count} -> {len(df)}")

        # 数据类型转换
        numeric_columns = [
            '最新价', '涨跌幅', '涨跌额', '成交量', '成交额',
            '市盈率', '市净率', '总市值', '流通市值'
        ]

        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)

        # 清理字符串字段
        df['股票名称'] = df['股票名称'].astype(str).str.strip()

        return df.to_dict('records')

    def save_comprehensive_data(self, stocks, filename='comprehensive_stocks.csv'):
        """保存全面股票数据"""
        if not stocks:
            self.logger.warning("没有股票数据可保存")
            return False

        try:
            os.makedirs('data', exist_ok=True)
            df = pd.DataFrame(stocks)

            filepath = os.path.join('data', filename)
            df.to_csv(filepath, index=False, encoding='utf-8-sig')

            # 保存简化版
            simple_columns = [
                '股票代码', '股票名称', '交易所', '市场类型',
                '最新价', '涨跌幅', '市盈率', '总市值', '更新时间'
            ]
            simple_df = df[simple_columns]
            simple_filepath = os.path.join('data', 'comprehensive_stocks_simple.csv')
            simple_df.to_csv(simple_filepath, index=False, encoding='utf-8-sig')

            self.logger.info(f"全面股票数据已保存到: {filepath}")

            # 检查长沙银行
            changsha_bank = df[df['股票代码'] == '601577']
            if len(changsha_bank) > 0:
                self.logger.info("🎉 长沙银行已成功获取！")
                print(changsha_bank[['股票代码', '股票名称', '交易所', '市场类型']].to_string(index=False))

            self._print_statistics(df)
            return True

        except Exception as e:
            self.logger.error(f"保存数据失败: {e}")
            return False

    def _print_statistics(self, df):
        """打印统计信息"""
        print("\n" + "="*60)
        print("📊 全面股票数据统计")
        print("="*60)

        total_stocks = len(df)

        # 交易所统计
        exchange_stats = df['交易所'].value_counts()
        print("🏛️ 交易所分布:")
        for exchange, count in exchange_stats.items():
            percentage = count / total_stocks * 100
            print(".1f")

        # 市场类型统计
        market_stats = df['市场类型'].value_counts()
        print("\n🏢 市场类型分布:")
        for market, count in market_stats.items():
            percentage = count / total_stocks * 100
            print(".1f")

        # 代码段统计
        code_prefixes = ['000', '002', '300', '600', '601', '603', '688']
        print("\n📋 代码段统计:")
        for prefix in code_prefixes:
            count = len(df[df['股票代码'].str.startswith(prefix)])
            percentage = count / total_stocks * 100
            print(".1f")

        # 特殊检查：长沙银行
        changsha_bank = df[df['股票代码'] == '601577']
        if len(changsha_bank) > 0:
            print("\n🎯 重点关注:")
            print("✅ 长沙银行(601577) 已成功获取")
        else:
            print("\n⚠️  提醒:")
            print("❌ 长沙银行(601577) 仍未获取到")

        print("="*60)

def main():
    """主函数"""
    print("=== 全面A股股票获取工具 ===\n")

    crawler = ComprehensiveStockCrawler()

    # 获取全面股票数据
    print("正在使用多种策略获取A股股票...")
    stocks = crawler.get_comprehensive_stocks()

    if stocks:
        print(f"✅ 获取到 {len(stocks)} 只股票")

        # 保存数据
        success = crawler.save_comprehensive_data(stocks)

        if success:
            print("✅ 数据保存成功！")
            print("📁 文件位置:")
            print("   - data/comprehensive_stocks.csv (完整版)")
            print("   - data/comprehensive_stocks_simple.csv (简化版)")
        else:
            print("❌ 数据保存失败")

    else:
        print("❌ 未获取到股票数据")

    print(f"\n程序运行完成！详细日志请查看 comprehensive_crawler.log")

if __name__ == "__main__":
    main()
